"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@workspace/ui/lib/utils";

interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
}

interface AnimatedTabGroupProps {
  tabs: Tab[];
  defaultTabId?: string;
  className?: string;
  onTabChange?: (tabId: string) => void;
}

export function AnimatedTabGroup({
  tabs,
  defaultTabId,
  className,
  onTabChange,
}: AnimatedTabGroupProps) {
  const [activeTabId, setActiveTabId] = useState(
    defaultTabId || (tabs.length > 0 ? tabs[0].id : "")
  );

  const handleTabChange = (event: React.MouseEvent, tabId: string) => {
    event.preventDefault();

    setActiveTabId(tabId);
    onTabChange?.(tabId);
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="flex border-b border-neutral-200 dark:border-neutral-800 mb-4">
        {tabs.map((tab) => (
          <motion.button
            key={tab.id}
            onClick={(event) => handleTabChange(event, tab.id)}
            className={cn(
              "px-4 py-2 text-sm font-medium transition-colors relative",
              activeTabId === tab.id
                ? "text-primary"
                : "text-muted-foreground hover:text-foreground"
            )}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {tab.label}
            {activeTabId === tab.id && (
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                layoutId="activeTab"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  type: "spring",
                  stiffness: 500,
                  damping: 30,
                }}
              />
            )}
          </motion.button>
        ))}
      </div>

      <AnimatePresence mode="wait">
        <motion.div
          key={activeTabId}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
          }}
          className="p-4"
        >
          {tabs.find((tab) => tab.id === activeTabId)?.content}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
