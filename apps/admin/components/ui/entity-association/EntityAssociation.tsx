"use client";

import React from "react";
import { Label } from "@workspace/ui/components/label";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@workspace/ui/components/combobox";
import MultipleSelector, {
  type Option,
} from "@workspace/ui/components/multiselect";
import { Link, Loader2 } from "lucide-react";
import { COMMON_ENTITY_TYPES } from "@/lib/constants/entity-types";
import { type UseEntityAssociationReturn } from "./useEntityAssociation";

export interface EntityAssociationProps {
  entityAssociation: UseEntityAssociationReturn;
  title?: string;
  description?: string;
  layout?: "vertical" | "horizontal";
  required?: boolean;
  className?: string;
  showIcon?: boolean;
  entityTypeFilter?: string[];
  entityTypePlaceholder?: string;
  entityPlaceholder?: string;
  customEntityRenderer?: (entity: any, entityType: string) => React.ReactNode;
  /** Enable multiple entity selection */
  multiselect?: boolean;
  /** Maximum number of entities that can be selected (only for multiselect mode) */
  maxSelected?: number;
  /** Placeholder for multiselect input */
  multiselectPlaceholder?: string;
}

export function EntityAssociation({
  entityAssociation,
  title = "Entity Association",
  description = "Associate with a specific entity like cargo, shipment, or customer",
  layout = "horizontal",
  required = false,
  className = "",
  showIcon = true,
  entityTypeFilter,
  entityTypePlaceholder = "Select entity type",
  entityPlaceholder,
  customEntityRenderer,
  multiselect = false,
  maxSelected = 10,
  multiselectPlaceholder,
}: EntityAssociationProps) {
  const {
    state,
    handleTableChange,
    handleIdChange,
    handleIdsChange,
    getEntityDisplayName,
  } = entityAssociation;

  // State for managing combobox open/close
  const [entityTypeOpen, setEntityTypeOpen] = React.useState(false);
  const [entityOpen, setEntityOpen] = React.useState(false);

  // Close entity combobox when entity type changes
  React.useEffect(() => {
    setEntityOpen(false);
  }, [state.selectedTable]);

  // Filter entity types if specified
  const availableEntityTypes = entityTypeFilter
    ? COMMON_ENTITY_TYPES.filter((type) =>
        entityTypeFilter.includes(type.value)
      )
    : COMMON_ENTITY_TYPES;

  // Generate dynamic placeholder for entity selection
  const dynamicEntityPlaceholder =
    entityPlaceholder ||
    (state.selectedTable
      ? `Select ${COMMON_ENTITY_TYPES.find((e) => e.value === state.selectedTable)?.label.toLowerCase()}`
      : "Select entity");

  // Generate dynamic placeholder for multiselect
  const dynamicMultiselectPlaceholder =
    multiselectPlaceholder ||
    (state.selectedTable
      ? `Select ${COMMON_ENTITY_TYPES.find((e) => e.value === state.selectedTable)?.label.toLowerCase()}...`
      : "Select entities...");

  // Convert entities to multiselect options
  const entityOptions: Option[] = React.useMemo(() => {
    return state.entities.map((entity) => ({
      value: entity.id,
      label: getEntityDisplayName(entity, state.selectedTable), // Always use string for multiselect
    }));
  }, [state.entities, state.selectedTable, getEntityDisplayName]);

  // Convert selected IDs to selected options
  const selectedOptions: Option[] = React.useMemo(() => {
    return state.selectedIds
      .map((id) => {
        const entity = state.entities.find((e) => e.id === id);
        if (!entity) return null;
        return {
          value: entity.id,
          label: getEntityDisplayName(entity, state.selectedTable), // Always use string for multiselect
        };
      })
      .filter((option): option is Option => option !== null);
  }, [
    state.selectedIds,
    state.entities,
    state.selectedTable,
    getEntityDisplayName,
  ]);

  // Handle multiselect change
  const handleMultiselectChange = React.useCallback(
    (options: Option[]) => {
      const ids = options.map((option) => option.value);
      handleIdsChange(ids);
    },
    [handleIdsChange]
  );

  const containerClass =
    layout === "vertical"
      ? "space-y-4"
      : "w-full flex flex-row items-start space-x-4";

  return (
    <div className={className}>
      <Label className="text-sm font-medium text-gray-700 mb-2">
        {showIcon && <Link className="h-4 w-4 inline mr-1" />}
        {title} {required && <span className="text-red-500">*</span>}
      </Label>
      {description && (
        <p className="text-xs text-gray-500 mb-3">{description}</p>
      )}

      <div className={containerClass}>
        {/* Entity Type Selection */}
        <div className={layout === "vertical" ? "w-full" : "flex-1"}>
          <Label className="text-xs text-gray-500 mb-1">Entity Type</Label>
          <Combobox open={entityTypeOpen} onOpenChange={setEntityTypeOpen}>
            <ComboboxTrigger placeholder={entityTypePlaceholder}>
              {state.selectedTable
                ? availableEntityTypes.find(
                    (entity) => entity.value === state.selectedTable
                  )?.label
                : entityTypePlaceholder}
            </ComboboxTrigger>
            <ComboboxContent>
              <ComboboxCommand>
                <ComboboxInput placeholder="Search entity types..." />
                <ComboboxList>
                  <ComboboxEmpty>No entity type found.</ComboboxEmpty>
                  <ComboboxGroup>
                    {availableEntityTypes.map((entity) => (
                      <ComboboxItem
                        key={entity.value}
                        value={entity.label} // Use label for search filtering
                        onSelect={(currentLabel) => {
                          // Find entity type by label to get the value
                          const selectedEntityType = availableEntityTypes.find(
                            (e) => e.label === currentLabel
                          );

                          if (selectedEntityType) {
                            handleTableChange(
                              selectedEntityType.value === state.selectedTable
                                ? ""
                                : selectedEntityType.value
                            );
                          }
                          setEntityTypeOpen(false);
                        }}
                      >
                        {entity.label}
                        <ComboboxItemIndicator
                          isSelected={state.selectedTable === entity.value}
                        />
                      </ComboboxItem>
                    ))}
                  </ComboboxGroup>
                </ComboboxList>
              </ComboboxCommand>
            </ComboboxContent>
          </Combobox>
        </div>

        {/* Entity Selection */}
        {state.selectedTable && (
          <div className={layout === "vertical" ? "w-full" : "flex-1"}>
            <Label className="text-xs text-gray-500 mb-1">
              Select{" "}
              {
                COMMON_ENTITY_TYPES.find((e) => e.value === state.selectedTable)
                  ?.label
              }
            </Label>

            {multiselect ? (
              /* Multiselect Mode */
              <MultipleSelector
                value={selectedOptions}
                onChange={handleMultiselectChange}
                options={entityOptions}
                placeholder={
                  state.loading ? "Loading..." : dynamicMultiselectPlaceholder
                }
                disabled={state.loading}
                maxSelected={maxSelected}
                emptyIndicator={
                  <p className="text-center text-sm text-gray-500">
                    No{" "}
                    {COMMON_ENTITY_TYPES.find(
                      (e) => e.value === state.selectedTable
                    )?.label.toLowerCase()}{" "}
                    found
                  </p>
                }
                loadingIndicator={
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </div>
                }
                className="w-full"
              />
            ) : (
              /* Single Select Mode (Original Combobox) */
              <Combobox open={entityOpen} onOpenChange={setEntityOpen}>
                <ComboboxTrigger
                  placeholder={
                    state.loading ? "Loading..." : dynamicEntityPlaceholder
                  }
                  className={
                    state.loading ? "opacity-50 cursor-not-allowed" : ""
                  }
                  disabled={state.loading}
                >
                  {state.selectedId
                    ? (() => {
                        const selectedEntity = state.entities.find(
                          (entity) => entity.id === state.selectedId
                        );
                        if (selectedEntity) {
                          return customEntityRenderer
                            ? customEntityRenderer(
                                selectedEntity,
                                state.selectedTable
                              )
                            : getEntityDisplayName(
                                selectedEntity,
                                state.selectedTable
                              );
                        }
                        // Fallback to a user-friendly display instead of raw ID
                        const entityTypeLabel =
                          COMMON_ENTITY_TYPES.find(
                            (e) => e.value === state.selectedTable
                          )?.label || state.selectedTable;
                        return `Selected ${entityTypeLabel} (${state.selectedId.slice(0, 8)}...)`;
                      })()
                    : state.loading
                      ? "Loading..."
                      : dynamicEntityPlaceholder}
                </ComboboxTrigger>
                <ComboboxContent className="max-h-[200px]">
                  <ComboboxCommand>
                    <ComboboxInput
                      placeholder={`Search ${COMMON_ENTITY_TYPES.find((e) => e.value === state.selectedTable)?.label.toLowerCase()}...`}
                    />
                    <ComboboxList>
                      {state.loading ? (
                        <div className="flex items-center justify-center p-2">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Loading...
                        </div>
                      ) : state.error ? (
                        <div className="text-red-500 text-sm p-2">
                          {state.error}
                        </div>
                      ) : state.entities.length === 0 ? (
                        <ComboboxEmpty>
                          No{" "}
                          {COMMON_ENTITY_TYPES.find(
                            (e) => e.value === state.selectedTable
                          )?.label.toLowerCase()}{" "}
                          found
                        </ComboboxEmpty>
                      ) : (
                        <ComboboxGroup>
                          {state.entities.map((entity) => {
                            const displayName = getEntityDisplayName(
                              entity,
                              state.selectedTable
                            );
                            return (
                              <ComboboxItem
                                key={entity.id}
                                value={displayName} // Use display name for search filtering
                                onSelect={(currentDisplayName) => {
                                  // Find entity by display name to get the ID
                                  const selectedEntity = state.entities.find(
                                    (e) =>
                                      getEntityDisplayName(
                                        e,
                                        state.selectedTable
                                      ) === currentDisplayName
                                  );

                                  if (selectedEntity) {
                                    handleIdChange(
                                      selectedEntity.id === state.selectedId
                                        ? ""
                                        : selectedEntity.id
                                    );
                                  }
                                  setEntityOpen(false);
                                }}
                              >
                                {customEntityRenderer
                                  ? customEntityRenderer(
                                      entity,
                                      state.selectedTable
                                    )
                                  : displayName}
                                <ComboboxItemIndicator
                                  isSelected={state.selectedId === entity.id}
                                />
                              </ComboboxItem>
                            );
                          })}
                        </ComboboxGroup>
                      )}
                    </ComboboxList>
                  </ComboboxCommand>
                </ComboboxContent>
              </Combobox>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
