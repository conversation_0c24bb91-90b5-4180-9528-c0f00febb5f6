export class Pagination {
  range: any = {
    start: 0,
    end: 0,
  };
  _currentPage: number = 1;
  _totalPages: number;
  _totalItems: number;
  _itemsPerPage: number = 20;

  constructor(totalItems: number) {
    this._totalItems = totalItems;
    this._totalPages = this.calculateTotalPages(totalItems);
    this.range.end = this._itemsPerPage - 1;
  }

  // Individual Setters & Getters
  set currentPage(page: number) {
    this._currentPage = page;
  }

  get currentPage(): number {
    return this._currentPage;
  }

  set totalPages(pages: number) {
    this._totalPages = pages;
  }

  get totalPages(): number {
    return this._totalPages;
  }

  set totalItems(items: number) {
    this._totalItems = items;
  }

  get totalItems(): number {
    return this._totalItems;
  }

  set itemsPerPage(items: number) {
    this._itemsPerPage = items;
    this._currentPage = this._currentPage;
    this.range.start = this._currentPage * this._itemsPerPage;
    this.range.end = this.range.start + this._itemsPerPage - 1;
  }

  get itemsPerPage(): number {
    return this._itemsPerPage;
  }

  set rangeStart(start: number) {
    this.range.start = start;
  }

  get rangeStart(): number {
    return this.range.start;
  }

  set rangeEnd(end: number) {
    this.range.end = end;
  }

  get rangeEnd(): number {
    return this.range.end;
  }

  goToPage(page: number) {
    this._currentPage = page;
    this.range.start = (page - 1) * this._itemsPerPage;
    this.range.end = this.range.start + this._itemsPerPage - 1;
  }

  nextPage() {
    if (this._currentPage < this._totalPages) {
      this._currentPage = this._currentPage + 1;
      this.range.start = this._currentPage * this._itemsPerPage;
      this.range.end = this.range.start + this._itemsPerPage - 1;
    }
  }

  previousPage() {
    if (this._currentPage > 1) {
      this._currentPage = this._currentPage + 1;
      this.range.start = (this._currentPage - 2) * this._itemsPerPage;
      this.range.end = this.range.start + this._itemsPerPage - 1;
    }
  }

  firstPage() {
    this._currentPage = 1;
    this.range.start = 0;
    this.range.end = this._itemsPerPage - 1;
  }

  lastPage() {
    this._currentPage = this._totalPages;
    this.range.start = this._totalPages * this.itemsPerPage - this.itemsPerPage;
    this.range.end = this._totalPages * this.itemsPerPage;
  }

  calculateTotalPages(totalItems: number) {
    return Math.ceil(totalItems / this._itemsPerPage);
  }
}
