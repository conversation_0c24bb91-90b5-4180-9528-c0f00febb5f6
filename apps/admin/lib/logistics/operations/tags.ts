import { BaseService } from "../base/service";
import {
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  StatusEnum,
} from "../types";

// Tag entity interface (matches actual database schema)
export interface Tag {
  id: string;
  created_at: string;
  name: string | null;
  origin: string | null;
  status: StatusEnum;
}

// Tag insert interface
export interface TagInsert {
  name: string;
  origin?: string | null;
  status?: StatusEnum;
}

// Tag update interface
export interface TagUpdate {
  name?: string;
  origin?: string | null;
  status?: StatusEnum;
}

// Tag with usage statistics (simplified since usage_count doesn't exist in schema)
export interface TagWithStats extends Tag {
  usage_count?: number;
  recent_usage?: string | null;
}

// Popular tags response
export interface PopularTagsResponse {
  tags: TagWithStats[];
}

// Popular tag suggestions (simplified without categories)
export const POPULAR_TAGS = [
  "revenue",
  "expenses",
  "recurring",
  "monthly",
  "quarterly",
  "annual",
  "operational",
  "administrative",
  "payment",
  "invoice",
  "billing",
  "freight",
  "cargo",
  "batch",
  "shipment",
  "delivery",
  "pickup",
  "processing",
  "completed",
  "pending",
  "urgent",
  "priority",
  "air-freight",
  "sea-freight",
  "road-transport",
  "warehouse",
  "customs",
  "documentation",
  "tracking",
  "location",
  "route",
  "vip",
  "regular",
  "new",
  "returning",
  "corporate",
  "individual",
  "international",
  "domestic",
  "premium",
  "standard",
  "automated",
  "manual",
  "notification",
  "alert",
  "backup",
  "maintenance",
  "update",
  "migration",
  "integration",
  "important",
  "archived",
  "draft",
  "review",
  "approved",
  "rejected",
  "temporary",
  "permanent",
  "test",
  "production",
] as const;

// TagService for the actual database schema
export class TagService extends BaseService<Tag, TagInsert, TagUpdate> {
  protected tableName = "tags";

  /**
   * Create a new tag
   */
  async createTag(data: TagInsert): Promise<ServiceResponse<Tag>> {
    try {
      // Check if tag with same name already exists
      const existingTag = await this.getTagByName(data.name);
      if (existingTag.success && existingTag.data) {
        return {
          success: false,
          data: null,
          error: `Tag with name "${data.name}" already exists`,
        };
      }

      return this.create(data);
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to create tag",
      };
    }
  }

  /**
   * Get all tags with optional filtering (excludes INACTIVE by default)
   */
  async getAllTags(
    params?: QueryParams,
    includeInactive: boolean = false
  ): Promise<ServiceListResponse<Tag>> {
    return this.getAll(params, "*", includeInactive);
  }

  /**
   * Get tag by name
   */
  async getTagByName(name: string): Promise<ServiceResponse<Tag>> {
    try {
      const result = await this.getAll({
        filters: { name },
        limit: 1,
      });

      if (!result.success) {
        return {
          success: false,
          data: null,
          error: result.error,
        };
      }

      const tag = result.data?.[0] || null;
      if (!tag) {
        return {
          success: false,
          data: null,
          error: `Tag with name "${name}" not found`,
        };
      }

      return {
        success: true,
        data: tag,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get tag by name",
      };
    }
  }

  /**
   * Get popular tags (most used) - simplified since usage_count doesn't exist
   */
  async getPopularTags(
    limit: number = 20
  ): Promise<ServiceResponse<TagWithStats[]>> {
    try {
      const result = await this.getAll({
        limit,
        column: "created_at",
        ascending: false,
      });

      if (!result.success) {
        return {
          success: false,
          data: [],
          error: result.error,
        };
      }

      return {
        success: true,
        data: result.data as unknown as TagWithStats[],
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: [],
        error: error.message || "Failed to get popular tags",
      };
    }
  }

  /**
   * Search tags by name or origin
   */
  async searchTags(
    searchTerm: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<Tag>> {
    return this.search(searchTerm, ["name", "origin"], params);
  }

  /**
   * Get or create tags by names
   */
  async getOrCreateTags(tagNames: string[]): Promise<ServiceResponse<Tag[]>> {
    try {
      const tags: Tag[] = [];

      for (const tagName of tagNames) {
        let tagResult = await this.getTagByName(tagName);

        if (!tagResult.success) {
          // Tag doesn't exist, create it
          const createResult = await this.createTag({
            name: tagName,
            status: "ACTIVE",
          });

          if (createResult.success && createResult.data) {
            tags.push(createResult.data);
          }
        } else if (tagResult.data) {
          tags.push(tagResult.data);
        }
      }

      return {
        success: true,
        data: tags,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: [],
        error: error.message || "Failed to get or create tags",
      };
    }
  }
}

// Create service instance
export const tagService = new TagService();

// Helper functions for tag management
export const TagHelpers = {
  /**
   * Get popular tags (simplified without categories)
   */
  getPopularTags: (): readonly string[] => {
    return POPULAR_TAGS;
  },

  /**
   * Validate tag name
   */
  validateTagName: (name: string): { isValid: boolean; error?: string } => {
    if (!name || name.trim().length === 0) {
      return { isValid: false, error: "Tag name is required" };
    }

    if (name.length > 50) {
      return {
        isValid: false,
        error: "Tag name must be 50 characters or less",
      };
    }

    if (!/^[a-zA-Z0-9\-_\s]+$/.test(name)) {
      return {
        isValid: false,
        error:
          "Tag name can only contain letters, numbers, hyphens, underscores, and spaces",
      };
    }

    return { isValid: true };
  },

  /**
   * Format tag name (lowercase, replace spaces with hyphens)
   */
  formatTagName: (name: string): string => {
    return name.toLowerCase().trim().replace(/\s+/g, "-");
  },

  /**
   * Get default tag color
   */
  getDefaultTagColor: (): string => {
    return "#6366F1"; // indigo
  },
};
