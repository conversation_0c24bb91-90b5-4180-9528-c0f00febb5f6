import { BaseService } from "../base/service";
import {
  Customer,
  CustomerInsert,
  CustomerUpdate,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
} from "../types";
import { notificationService } from "../system/notifications";
import { ServerResponse } from "http";

export class CustomerService extends BaseService<
  Customer,
  CustomerInsert,
  CustomerUpdate
> {
  protected tableName = "customers";

  // Get customer with cargo count
  async getCustomerWithCargoStats(id: string): Promise<
    ServiceResponse<
      Customer & {
        cargoStats: {
          totalCargos: number;
          activeCargos: number;
          completedCargos: number;
          pendingCargos: number;
        };
      }
    >
  > {
    try {
      // Get customer data
      const customerResult = await this.getById(id);
      if (!customerResult.success || !customerResult.data) {
        return customerResult as ServiceResponse<
          Customer & { cargoStats: any }
        >;
      }

      // Get cargo statistics
      const { data: cargos, error: cargoError } = await this.supabase
        .from("cargos")
        .select("status")
        .eq("customer_id", id);

      if (cargoError) {
        return {
          data: null,
          error: cargoError.message,
          success: false,
        };
      }

      const totalCargos = cargos?.length || 0;
      const activeCargos =
        cargos?.filter((c) =>
          ["CREATED", "PROCESSING", "IN_TRANSIT"].includes(c.status || "")
        ).length || 0;
      const completedCargos =
        cargos?.filter((c) => c.status === "COMPLETED").length || 0;
      const pendingCargos =
        cargos?.filter((c) => c.status === "PENDING").length || 0;

      return {
        data: {
          ...customerResult.data,
          cargoStats: {
            totalCargos,
            activeCargos,
            completedCargos,
            pendingCargos,
          },
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get customer with cargo stats",
        success: false,
      };
    }
  }

  // Get all customers with cargo counts
  async getAllCustomersWithCargoStats(
    params?: QueryParams
  ): Promise<ServiceListResponse<Customer & { cargoCount: number }>> {
    try {
      let query = this.supabase.from("customers").select(
        `
          *,
          cargos(count)
        `,
        { count: "exact" }
      );

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      // Transform the data to include cargo count
      const transformedData =
        data?.map((customer) => ({
          ...customer,
          cargoCount: customer.cargos?.[0]?.count || 0,
        })) || [];

      return {
        data: transformedData as any,
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get customers with cargo stats",
        success: false,
      };
    }
  }

  // Get active customers only
  async getActiveCustomers(
    params?: QueryParams
  ): Promise<ServiceListResponse<Customer>> {
    const filters = { ...params?.filters, status: "ACTIVE" as const };
    return this.getAll({ ...params, filters });
  }

  // Search customers by name, email, phone, location
  async searchCustomers(
    searchTerm: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<Customer>> {
    return this.search(
      searchTerm,
      ["name", "email", "phone", "location"],
      params
    );
  }

  // Check if customer email exists
  async isEmailExists(
    email: string,
    excludeId?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from("customers")
        .select("id, name")
        .eq("email", email);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data,
        check: (data || []).length > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check customer email",
        success: false,
      };
    }
  }

  // Check if customer name exists
  async isNameExists(
    name: string,
    excludeId?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from("customers")
        .select("id, name")
        .eq("name", name);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data,
        check: (data || []).length > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check customer name",
        success: false,
      };
    }
  }

  // Check if customer phone exists
  async isPhoneExists(
    phone: string,
    excludeId?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from("customers")
        .select("id, name")
        .eq("phone", phone);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data,
        check: (data || []).length > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check customer phone",
        success: false,
      };
    }
  }

  // Comprehensive validation for unique constraints
  async validateCustomerUniqueness(
    data: { name?: string; email?: string; phone?: string },
    excludeId?: string
  ): Promise<ServiceResponse<{ isValid: boolean; conflicts: string[] }>> {
    try {
      const conflicts: string[] = [];

      // Check name uniqueness
      if (data.name) {
        const nameExists: any = await this.isNameExists(data.name, excludeId);
        if (!nameExists.success) {
          return {
            data: null,
            error: nameExists.error,
            success: false,
          };
        }
        if (nameExists.check) {
          conflicts.push("Customer name already exists");
        }
      }

      // Check email uniqueness
      if (data.email) {
        const emailExists: any = await this.isEmailExists(
          data.email,
          excludeId
        );
        if (!emailExists.success) {
          return {
            data: null,
            error: emailExists.error,
            success: false,
          };
        }
        if (emailExists.check) {
          let customer = emailExists?.data[0] || "Unknown Customer";
          conflicts.push("Email already belongs to: " + customer.name);
        }
      }

      // Check phone uniqueness
      if (data.phone) {
        const phoneExists: any = await this.isPhoneExists(
          data.phone,
          excludeId
        );
        if (!phoneExists.success) {
          return {
            data: null,
            error: phoneExists.error,
            success: false,
          };
        }
        if (phoneExists.check) {
          let customer = phoneExists?.data[0] || "Unknown Customer";
          conflicts.push("Phone number already belongs to: " + customer?.name);
        }
      }

      return {
        data: {
          isValid: conflicts.length === 0,
          conflicts,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to validate customer uniqueness",
        success: false,
      };
    }
  }

  // Generate customer code in format {firstname initial}{lastname initial}-{year}-{month}-{index}
  private async generateCustomerCode(customerName: string): Promise<string> {
    try {
      // Extract initials from name using simple space splitting
      const nameParts = customerName.trim().split(" ");
      const firstInitial = nameParts[0]?.charAt(0).toUpperCase() || "X";
      const lastInitial =
        nameParts.length > 1
          ? nameParts[nameParts.length - 1]?.charAt(0).toUpperCase()
          : "X";

      // Get current date
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");

      // Get count of customers created this month with same initials
      const startOfMonth = new Date(year, now.getMonth(), 1).toISOString();
      const endOfMonth = new Date(
        year,
        now.getMonth() + 1,
        0,
        23,
        59,
        59
      ).toISOString();

      const { data: existingCustomers, error } = await this.supabase
        .from("customers")
        .select("code")
        .like("code", `${firstInitial}${lastInitial}-${year}-${month}-%`)
        .gte("created_at", startOfMonth)
        .lte("created_at", endOfMonth);

      if (error) {
        console.error("Error fetching existing customers:", error);
        // Fallback to timestamp-based index
        const index = String(now.getTime()).slice(-3);
        return `${firstInitial}${lastInitial}-${year}-${month}-${index}`;
      }

      // Calculate next index
      const index = String((existingCustomers?.length || 0) + 1).padStart(
        3,
        "0"
      );

      return `${firstInitial}${lastInitial}-${year}-${month}-${index}`;
    } catch (error) {
      console.error("Error generating customer code:", error);
      // Fallback code
      const now = new Date();
      return `XX-${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getTime()).slice(-3)}`;
    }
  }

  // Create customer with validation
  async createCustomer(
    data: CustomerInsert
  ): Promise<ServiceResponse<Customer>> {
    try {
      // Validate unique constraints for name, email, and phone
      const validation = await this.validateCustomerUniqueness({
        name: data.name,
        email: data.email || undefined,
        phone: data.phone || undefined,
      });

      if (!validation.success) {
        return {
          data: null,
          error: validation.error,
          success: false,
        };
      }

      if (!validation.data?.isValid) {
        return {
          data: null,
          error: validation.data?.conflicts.join(", ") || "Validation failed",
          success: false,
        };
      }

      // Generate customer code if not provided
      if (!data.code) {
        data.code = await this.generateCustomerCode(data.name);
      }

      // Create the customer
      const result = await this.create(data);

      // If customer creation was successful, create notification
      if (result.success && result.data && data.account_id) {
        try {
          await notificationService.createTargetedNotification({
            account_id: data.account_id,
            name: "New Customer Created",
            message: `Customer "${data.name}" has been successfully created and added to the CRM.`,
            associated_table: "customers",
            associated_id: result.data.id,
            to: "*",
            details: {
              customerName: data.name,
              customerEmail: data.email,
              customerPhone: data.phone,
              customerLocation: data.location,
              timestamp: new Date().toISOString(),
            },
          });
        } catch (notificationError) {
          // Don't fail customer creation if notification fails
        }
      }

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create customer",
        success: false,
      };
    }
  }

  // Update customer with validation
  async updateCustomer(
    id: string,
    data: CustomerUpdate
  ): Promise<ServiceResponse<Customer>> {
    try {
      // Validate unique constraints for name, email, and phone (excluding current customer)
      const validation = await this.validateCustomerUniqueness(
        {
          name: data.name,
          email: data.email || undefined,
          phone: data.phone || undefined,
        },
        id
      );

      if (!validation.success) {
        return {
          data: null,
          error: validation.error,
          success: false,
        };
      }

      if (!validation.data?.isValid) {
        return {
          data: null,
          error: validation.data?.conflicts.join(", ") || "Validation failed",
          success: false,
        };
      }

      return this.update(id, data);
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update customer",
        success: false,
      };
    }
  }

  // Get customer by email
  async getCustomerByEmail(email: string): Promise<ServiceResponse<Customer>> {
    try {
      const { data, error } = await this.supabase
        .from("customers")
        .select("*")
        .eq("email", email)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get customer by email",
        success: false,
      };
    }
  }

  // Get customer by phone
  async getCustomerByPhone(phone: string): Promise<ServiceResponse<Customer>> {
    try {
      const result = await this.getAll({
        filters: { phone },
        limit: 1,
      });

      if (!result.success) {
        return {
          data: null,
          error: result.error,
          success: false,
        };
      }

      const customer = result.data?.[0] || null;
      if (!customer) {
        return {
          data: null,
          error: "Customer not found",
          success: false,
        };
      }

      return {
        data: customer,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get customer by phone",
        success: false,
      };
    }
  }

  // Get customers by account (created by specific account)
  async getCustomersByAccount(
    accountId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<Customer>> {
    const filters = { ...params?.filters, account_id: accountId };
    return this.getAll({ ...params, filters });
  }

  // Get customer statistics
  async getCustomerStats(): Promise<
    ServiceResponse<{
      totalCustomers: number;
      activeCustomers: number;
      inactiveCustomers: number;
      customersWithEmail: number;
      customersWithPhone: number;
      customersWithLocation: number;
      topCustomersByCargoCount: Array<{
        customerId: string;
        customerName: string;
        cargoCount: number;
      }>;
    }>
  > {
    try {
      // Get basic customer stats
      const { data: customers, error: customerError } = await this.supabase
        .from("customers")
        .select("id, name, status, email, phone, location");

      if (customerError) {
        return {
          data: null,
          error: customerError.message,
          success: false,
        };
      }

      const totalCustomers = customers?.length || 0;
      const activeCustomers =
        customers?.filter((c) => c.status === "ACTIVE").length || 0;
      const inactiveCustomers = totalCustomers - activeCustomers;
      const customersWithEmail = customers?.filter((c) => c.email).length || 0;
      const customersWithPhone = customers?.filter((c) => c.phone).length || 0;
      const customersWithLocation =
        customers?.filter((c) => c.location).length || 0;

      // Get top customers by cargo count
      const { data: cargoData, error: cargoError } = await this.supabase
        .from("cargos")
        .select("customer_id, customers(id, name)");

      if (cargoError) {
        return {
          data: null,
          error: cargoError.message,
          success: false,
        };
      }

      // Count cargos per customer
      const customerCargoCounts = new Map<
        string,
        { name: string; count: number }
      >();
      cargoData?.forEach((cargo) => {
        if (cargo.customer_id && cargo.customers) {
          const customerId = cargo.customer_id;
          const customerName = (cargo.customers as any)?.name || "Unknown";
          const current = customerCargoCounts.get(customerId) || {
            name: customerName,
            count: 0,
          };
          customerCargoCounts.set(customerId, {
            ...current,
            count: current.count + 1,
          });
        }
      });

      // Get top 5 customers by cargo count
      const topCustomersByCargoCount = Array.from(customerCargoCounts.entries())
        .map(([id, data]) => ({
          customerId: id,
          customerName: data.name,
          cargoCount: data.count,
        }))
        .sort((a, b) => b.cargoCount - a.cargoCount)
        .slice(0, 5);

      return {
        data: {
          totalCustomers,
          activeCustomers,
          inactiveCustomers,
          customersWithEmail,
          customersWithPhone,
          customersWithLocation,
          topCustomersByCargoCount,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get customer statistics",
        success: false,
      };
    }
  }

  // Get customers for dropdown/selection
  async getCustomersForSelection(
    searchTerm?: string,
    params?: QueryParams
  ): Promise<
    ServiceListResponse<Pick<Customer, "id" | "name" | "email" | "phone">>
  > {
    try {
      const filters: any = { status: "ACTIVE" };

      // Add search filter if provided
      if (searchTerm) {
        filters.query = searchTerm; // This will be handled by BaseService applyFilters
      }

      const result = await this.getAll(
        {
          ...params,
          filters,
          limit: params?.limit || 50,
          column: "updated_at",
          ascending: false,
        },
        "id, name, email, phone"
      );

      return {
        data: result.data as Pick<
          Customer,
          "id" | "name" | "email" | "phone"
        >[],
        error: result.error,
        success: result.success,
        count: result.count,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to fetch customers for selection",
        success: false,
      };
    }
  }

  // Merge duplicate customers (transfer all cargos to target customer and delete source)
  async mergeCustomers(
    sourceCustomerId: string,
    targetCustomerId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Transfer all cargos from source to target
      const { error: cargoError } = await this.supabase
        .from("cargos")
        .update({
          customer_id: targetCustomerId,
        })
        .eq("customer_id", sourceCustomerId);

      if (cargoError) {
        return {
          data: null,
          error: cargoError.message,
          success: false,
        };
      }

      // Delete source customer
      const deleteResult = await this.delete(sourceCustomerId, true);
      if (!deleteResult.success) {
        return deleteResult;
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to merge customers",
        success: false,
      };
    }
  }
}

// Export singleton instance
export const customerService = new CustomerService();
