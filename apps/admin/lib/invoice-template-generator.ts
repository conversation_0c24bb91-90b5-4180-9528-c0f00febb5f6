import { generatePD<PERSON>romJSO<PERSON> } from "@workspace/pdf-generator";
import type { JSONDocumentData, PDFOptions } from "@workspace/pdf-generator";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";

import QRCode from "qrcode";
import type {
  ReleaseAuthorizationData,
  InvoiceTemplateOptions,
  GenerateInvoiceResult,
  QRCodeData,
  CargoItem,
} from "./types/invoice-template";

// Import invoice types
import type {
  InvoiceData,
  InvoiceWithCustomer,
} from "./logistics/operations/invoices";
import { DEFAULT_COMPANY_CONFIG } from "./utils";
import { DocumentService } from "./logistics";
import {
  getDimensionUnitAbbreviation,
  getWeightUnitAbbreviation,
} from "./utils/unit-mappings";

// General invoice template types
export interface GeneralInvoiceTemplateData {
  invoice: InvoiceData;
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    location?: string;
  };
  company: {
    name: string;
    address: string;
    phone: string;
    email: string;
    logo?: string;
  };
  lineItems: {
    tracking_number: string;
    china_tracking_number: string;
    description: string;
    weight_value: number;
    weight_unit: string;
    cbm_value: number;
    cbm_unit: string;
    ctn: number;
    quantity: number;
    factor_unit: any;
    factor_value: any;
    unitPrice: number;
    total: number;
  }[];
  totals: {
    subtotal: number;
    discount?: number;
    total: number;
    // Extended totals for line items
    totalCBM: number;
    totalCTN: number;
    totalWeight: number;
    totalUnitPrice: number;
    totalPrice: number;
  };
  batch?: {
    id: string;
    code: string;
    name: string;
    type: "AIR" | "SEA" | string;
  };
}

export interface GeneratedInvoiceDocument {
  url: string;
  fileName: string;
  documentNumber: string;
  metadata: {
    invoiceId: string;
    customerId?: string;
    generatedAt: string;
    templateVersion: string;
  };
}

function simplifyTrackingNumber(trackingNumber: string) {
  if (!trackingNumber) return " - ";
  let parts = trackingNumber.split("/");
  let last = parts[parts.length - 1];
  let secondToLast = parts[parts.length - 2];
  return `${secondToLast}/${last}`;
}

/**
 * Generates a QR code as base64 data URL
 */
async function generateQRCode(
  data: QRCodeData,
  size: number = 200
): Promise<string> {
  try {
    const qrData = `${window.location.origin}/release-authorization/${data.id}`;

    const qrCodeDataURL = await QRCode.toDataURL(qrData, {
      width: size,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    return qrCodeDataURL;
  } catch (error) {
    console.error("Error generating QR code:", error);
    throw new Error("Failed to generate QR code");
  }
}

/**
 * Formats currency values
 */
function formatCurrency(amount: number, currency: string = "USD"): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

/**
 * Formats date and time
 */
function formatDateTime(date: string | Date): { date: string; time: string } {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  return {
    date: dateObj.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
    time: dateObj.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }),
  };
}

/**
 * Creates cargo items table data
 */
function createCargoTable(cargoItems: CargoItem[]): {
  head: string[];
  body: string[][];
} {
  const head = [
    "Tracking #",
    "Description",
    "Customer",
    "Qty",
    "Weight",
    "Dimensions",
    "Value",
  ];

  const body = cargoItems.map((item) => [
    item.trackingNumber,
    `${item.description}\n(${item.category})`,
    `${item.customerName}${item.customerCompany ? `\n${item.customerCompany}` : ""}`,
    item.quantity.toString(),
    `${item.weight} ${item.weightUnit}`,
    item.dimensions
      ? `${item.dimensions.length}×${item.dimensions.width}×${item.dimensions.height} ${item.dimensions.unit}`
      : "N/A",
    formatCurrency(item.value, item.currency),
  ]);

  return { head, body };
}

/**
 * Main function to generate release authorization invoice PDF
 */
export async function generateReleaseAuthorizationInvoice(
  data: ReleaseAuthorizationData,
  options: InvoiceTemplateOptions = {}
): Promise<GenerateInvoiceResult> {
  try {
    // Merge with default options
    const opts: InvoiceTemplateOptions = {
      format: "A4",
      orientation: "portrait",
      primaryColor: "#1e40af",
      secondaryColor: "#64748b",
      fontFamily: "helvetica",
      fontSize: 10,
      showCompanyLogo: true,
      showCargoImages: false,
      showDetailedCargoInfo: true,
      showPaymentDetails: true,
      qrCodeSize: 40,
      qrCodePosition: "bottom-right",
      language: "en",
      ...options,
    };

    // Generate QR code
    const qrCodeDataURL = await generateQRCode(data.qrCode, opts.qrCodeSize);

    // Format dates
    const generatedDateTime = formatDateTime(data.metadata.generatedDate);
    const authorizedDateTime = formatDateTime(data.qrCode.authorizedDate);

    // Create cargo table
    const cargoTable = createCargoTable(data.cargoItems);

    // Calculate totals
    const totalWeight = data.cargoItems.reduce(
      (sum, item) => sum + item.weight,
      0
    );
    const totalValue = data.cargoItems.reduce(
      (sum, item) => sum + item.value,
      0
    );

    // Build PDF content
    const pdfContent: JSONDocumentData = {
      title: "RELEASE AUTHORIZATION DOCUMENT",
      metadata: {
        author: data.personnel.authorizedBy,
        subject: `Release Authorization - ${data.metadata.documentNumber}`,
        keywords: "release,authorization,cargo,logistics,shamwaa",
        creator: "Shamwaa Africa",
      },
      content: [
        // Header with company info
        {
          type: "image",
          value: {
            url: DEFAULT_COMPANY_CONFIG.logo,
            width: 100,
            height: 40,
          },
          style: {
            alignment: "center",
          },
        },
        {
          type: "paragraph",
          value: `${data.company.address || DEFAULT_COMPANY_CONFIG.address}\nPhone: ${data.company.phone || DEFAULT_COMPANY_CONFIG.phone} | Email: ${data.company.email || DEFAULT_COMPANY_CONFIG.email}`,
          style: {
            fontSize: 9,
            alignment: "center",
            color: opts.secondaryColor,
          },
        },

        // Document metadata
        {
          type: "heading",
          value: "CARGO RELEASE AUTHORIZATION",
          style: {
            fontSize: 16,
            bold: true,
            alignment: "center",
          },
        },
        {
          type: "paragraph",
          value: `Document No: ${data.metadata.documentNumber}\nGenerated: ${generatedDateTime.date} at ${generatedDateTime.time}\nLocation: ${data.metadata.location}`,
          style: {
            fontSize: 10,
            alignment: "center",
          },
        },

        // Freight and batch information
        {
          type: "heading",
          value: "FREIGHT & BATCH INFORMATION",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: `Freight: ${data.freight.name} (${data.freight.type})\nRoute: ${data.freight.origin} → ${data.freight.destination}\nBatch Code: ${data.batch.code}\nTotal Cargo Items: ${data.batch.cargoCount} | Total Customers: ${data.batch.customerCount}\nBatch Weight: ${data.batch.totalWeight} ${data.batch.weightUnit} | Value: ${formatCurrency(data.batch.totalValue, data.batch.currency)}`,
        },

        // Authorization details
        {
          type: "heading",
          value: "AUTHORIZATION DETAILS",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: `This document authorizes the release of cargo items listed below to their respective customers or authorized representatives.\n\nAuthorized by: ${data.personnel.authorizedBy} (${data.personnel.role})\nDepartment: ${data.personnel.department}\nAuthorization Date: ${authorizedDateTime.date} at ${authorizedDateTime.time}\nRelease Code: ${data.qrCode.releaseCode}`,
        },

        // Cargo manifest table
        {
          type: "heading",
          value: "CARGO MANIFEST",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "table",
          value: cargoTable,
          style: {
            fontSize: 8,
          },
        },

        // Summary
        {
          type: "paragraph",
          value: `SUMMARY: ${data.cargoItems.length} cargo items | Total Weight: ${totalWeight.toFixed(2)} kg | Total Value: ${formatCurrency(totalValue)}`,
        },
      ],
    };

    // Add payment information if enabled
    if (opts.showPaymentDetails && data.payment) {
      pdfContent.content.push(
        {
          type: "heading",
          value: "PAYMENT CONFIRMATION",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: `Payment Status: ${data.payment.status}\nTotal Amount: ${formatCurrency(data.payment.totalAmount, data.payment.currency)}\nPaid Amount: ${formatCurrency(data.payment.paidAmount, data.payment.currency)}\n${data.payment.paymentMethod ? `Payment Method: ${data.payment.paymentMethod}` : ""}\n${data.payment.paymentReference ? `Reference: ${data.payment.paymentReference}` : ""}`,
        }
      );
    }

    // Add QR code
    pdfContent.content.push(
      {
        type: "heading",
        value: "VERIFICATION QR CODE",
        style: {
          fontSize: 12,
          bold: true,
        },
      },
      {
        type: "image",
        value: {
          url: qrCodeDataURL,
          width: opts.qrCodeSize || 30,
          height: opts.qrCodeSize || 30,
        },
      },
      {
        type: "paragraph",
        value: `Scan this QR code to verify the release authorization.\nRelease Code: ${data.qrCode.releaseCode}`,
        style: {
          fontSize: 9,
          color: opts.secondaryColor,
        },
      }
    );

    // Attachments section
    if (data.attachments && data.attachments.length > 0) {
      // Add large spacer to push attachments to next page
      pdfContent.content.push({
        type: "paragraph",
        value: "\n",
        style: {
          fontSize: 12,
        },
      });

      pdfContent.content.push({
        type: "heading",
        value: "DOCUMENT ATTACHMENTS",
        style: {
          fontSize: 14,
          bold: true,
        },
      });

      let document = new DocumentService();

      let attachmentsWithUrls = data.attachments.map(async (attachment) => {
        let signedURL = await document.getDocumentDownloadUrl(attachment.path);
        return {
          ...attachment,
          uri: signedURL.data,
        };
      });

      data.attachments = await Promise.all(attachmentsWithUrls);

      // Group attachments by type for better organization
      const imageAttachments = data.attachments.filter((attachment: any) => {
        const type = attachment.path.split(".").pop()?.toLowerCase();
        return ["png", "jpeg", "jpg", "gif", "bmp", "webp"].includes(
          type || ""
        );
      });

      const documentAttachments = data.attachments.filter((attachment: any) => {
        const type = attachment.path.split(".").pop()?.toLowerCase();
        return !["png", "jpeg", "jpg", "gif", "bmp", "webp"].includes(
          type || ""
        );
      });

      // First, show document attachments in a table
      if (documentAttachments.length > 0) {
        pdfContent.content.push({
          type: "heading",
          value: "DOCUMENT FILES",
          style: {
            fontSize: 12,
            bold: true,
          },
        });

        const documentRows = documentAttachments.map(
          (attachment: any, index) => [
            (index + 1).toString(),
            attachment.name,
            attachment.category || "Document",
            attachment.description || "N/A",
            attachment.uploadedAt
              ? new Date(attachment.uploadedAt).toLocaleDateString()
              : "N/A",
          ]
        );

        const documentTable = {
          head: [
            "#",
            "Document Name",
            "Category",
            "Description",
            "Upload Date",
          ],
          body: documentRows,
        };

        pdfContent.content.push({
          type: "table",
          value: documentTable,
          style: {
            fontSize: 9,
          },
        });
      }

      // Then, show image attachments with proper page break handling
      if (imageAttachments.length > 0) {
        pdfContent.content.push({
          type: "heading",
          value: "IMAGE ATTACHMENTS",
          style: {
            fontSize: 12,
            bold: true,
          },
        });

        imageAttachments.forEach((attachment: any, index) => {
          const count = index + 1;

          // Add large spacing before each image to encourage page breaks
          if (index > 0) {
            pdfContent.content.push({
              type: "paragraph",
              value: "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n",
              style: {
                fontSize: 12,
              },
            });
          }

          // Image title
          pdfContent.content.push({
            type: "heading",
            value: `${count}. ${attachment.name}`,
            style: {
              fontSize: 10,
              bold: true,
            },
          });

          // Image description if available
          if (attachment.description) {
            pdfContent.content.push({
              type: "paragraph",
              value: attachment.description,
              style: {
                fontSize: 8,
                italic: true,
                color: "#666666",
              },
            });
          }

          // Image with proper sizing to fit page
          pdfContent.content.push({
            type: "image",
            value: {
              url: attachment.uri,
              width: 220, // Optimized width for better visibility
              height: 150, // Optimized height for better visibility
            },
            style: {
              alignment: "center",
            },
          });

          // Add spacing after image
          pdfContent.content.push({
            type: "paragraph",
            value: "",
            style: {
              fontSize: 12,
            },
          });
        });
      }

      // Summary of all attachments
      pdfContent.content.push({
        type: "paragraph",
        value: `Total Attachments: ${data.attachments.length} (${documentAttachments.length} documents, ${imageAttachments.length} images)`,
        style: {
          fontSize: 9,
          color: opts.secondaryColor || "#6b7280",
          italic: true,
          alignment: "center",
        },
      });

      // Add attachment summary
      pdfContent.content.push({
        type: "paragraph",
        value: `Total Attachments: ${data.attachments.length}`,
        style: {
          fontSize: 9,
          color: opts.secondaryColor || "#6b7280",
          italic: true,
        },
      });
    }

    // Add notes if provided
    if (data.notes || data.specialInstructions) {
      pdfContent.content.push(
        {
          type: "heading",
          value: "ADDITIONAL NOTES",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: `${data.notes || ""}\n${data.specialInstructions ? `Special Instructions: ${data.specialInstructions}` : ""}`,
        }
      );
    }

    // PDF generation options
    const pdfOptions: PDFOptions = {
      format: opts.format?.toLowerCase() || "a4",
      orientation: opts.orientation || "portrait",
      fontName: opts.fontFamily || "helvetica",
      fontSize: opts.fontSize || 10,
      margins: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20,
      },
      showHeader: true,
      headerText: `${data.company.name || DEFAULT_COMPANY_CONFIG.name} - Release Authorization`,
      showFooter: true,
      footerText: `Document: ${data.metadata.documentNumber} | Generated: ${generatedDateTime.date}`,
      includePageNumbers: true,
    };

    // Generate PDF
    const pdfBlob = await generatePDFFromJSON(pdfContent, pdfOptions);

    // Generate filename
    const fileName = `release-authorization-${data.metadata.documentNumber}-${data.batch.code}.pdf`;

    return {
      success: true,
      data: {
        pdfBlob,
        documentNumber: data.metadata.documentNumber,
        qrCodeData: JSON.stringify(data.qrCode),
        fileName,
      },
    };
  } catch (error) {
    console.error("Error generating release authorization invoice:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Utility function to download the generated PDF
 */
export function downloadPDF(blob: Blob, fileName: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Utility function to generate document number
 */
export function generateDocumentNumber(prefix: string = "REL"): string {
  return codeGeneratorService.generateDocumentNumber({
    prefix,
    includeDate: true,
  });
}

/**
 * Parse line items from JSONB data
 */
export function parseLineItems(
  lineItemData: any,
  batchType: string
): GeneralInvoiceTemplateData["lineItems"] {
  if (!lineItemData || !Array.isArray(lineItemData)) {
    return [];
  }

  let data = lineItemData.map((item: any, index: number) => {
    let metrics: any = {};

    if (batchType === "AIR") {
      metrics = {
        weight_value: item.weight_value.toFixed(2) || "",
        weight_unit: getWeightUnitAbbreviation(item.weight_unit),
      };
    } else if (batchType === "SEA") {
      metrics = {
        cbm_value: item.cbm_value.toFixed(2) || "",
        cbm_unit: getDimensionUnitAbbreviation(item.cbm_unit),
      };
    }

    return {
      index: `${index + 1}`,
      cargo_id: item.cargo_id,
      tracking_number: item.tracking_number || "N/A",
      china_tracking_number: item.china_tracking_number || "N/A",
      description: item.description || "N/A",
      ...metrics,
      ctn: item.ctn || "N/A",
      unitPrice: item.unitPrice ? `$${item.unitPrice.toLocaleString()}` : "N/A",
      total: item.total ? `$${item.total.toLocaleString()}` : "N/A",
    };
  });

  const { totalCBM, totalCTN, totalWeight, totalPrice } =
    calculateTotals(lineItemData);

  let metrics: any = {};

  if (batchType === "AIR") {
    metrics = {
      weight_value: totalWeight.toFixed(2) || "",
      weight_unit: getWeightUnitAbbreviation("KILOGRAMS"),
    };
  } else if (batchType === "SEA") {
    metrics = {
      cbm_value: totalCBM.toFixed(2) || "",
      cbm_unit: getDimensionUnitAbbreviation("METER_CUBIC"),
    };
  }

  data.push({
    index: "",
    tracking_number: "",
    china_tracking_number: "",
    description: "-- Totals --",
    ...metrics,
    ctn: totalCTN || "N/A",
    unitPrice: "",
    total: totalPrice ? `$${totalPrice.toLocaleString()}` : "N/A",
  });

  return data;
}

/**
 * Calculate totals from line items
 */
export function calculateTotals(
  lineItems: GeneralInvoiceTemplateData["lineItems"]
): GeneralInvoiceTemplateData["totals"] {
  const subtotal = lineItems.reduce((sum, item) => sum + item.total, 0);
  const total = subtotal;

  // Calculate extended totals for line items
  const totalCBM = lineItems.reduce(
    (sum, item) => sum + (item.cbm_value || 0),
    0
  );

  const totalCTN = lineItems.reduce((sum, item) => sum + (item.ctn || 0), 0);

  const totalWeight = lineItems.reduce(
    (sum, item) => sum + (item.weight_value || 0),
    0
  );

  const totalUnitPrice = lineItems.reduce(
    (sum, item) => sum + (item.unitPrice || 0),
    0
  );

  const totalPrice = lineItems.reduce(
    (sum, item) => sum + (item.total || 0),
    0
  );

  return {
    subtotal,
    total,
    totalCBM,
    totalCTN,
    totalWeight,
    totalUnitPrice,
    totalPrice,
  };
}

/**
 * Prepare template data from invoice
 */
export function prepareInvoiceTemplateData(
  invoice: InvoiceWithCustomer,
  companyInfo?: Partial<GeneralInvoiceTemplateData["company"]>
): GeneralInvoiceTemplateData {
  const batchType = (invoice as any).batch?.freight_type || "SEA";
  const lineItems = parseLineItems(invoice.line_items, batchType);
  const totals = calculateTotals(invoice.line_items);

  const terms_and_conditions = `1. This Invoice Must Be Paid Within 24 Hours. And If The Dollar Rates Changes Anytime Before Payments Are Made You Will Be Reinvoiced.
2. SHAMWAA Will Not Be Responsible For Any Delayed Goods As A Result Of Failure To Complete Invoice Payment On Time
3. All Payment Receipts Should Be Presented To The Company For Confirmation. Payment Made Without Office Confirmation Will Be Considered Unreceived And The Respective Goods Unpaid For.
4. Invoice Price Includes All Charges (shipping + Customs + Clearance)
5. Any Complaints In Respect To This Invoice Should Be Raised To The Company Within 24 Hours Of Issue. The Company Is Not Obligated To Make Any Amendments To The Invoice After The 24-hour Period Unless It Seems Right Based On Company Policies, Procedures And Accounting Regulations.
All Payments Must Be Made To The Company Bank Account
6. The USD rate is 1usd = 2800tsh and it can change due to changes in usd rates.
7. This is a reminder that you are required to collect your goods within 15 days after they arrive at our warehouse.
8. Please note that if the goods remain beyond the stipulated period, a storage fee of USD 20 per day will be charged for a period of 60 days. After this period, the goods will be auctioned to cover office expenses.`;

  const notes = `Please note that if the goods remain beyond the stipulated period, a storage fee of USD 20 per day will be charged for a period of 60 days. After this period, the goods will be auctioned to cover office expenses.`;

  // Extract batch information if available
  const batch = (invoice as any).batch
    ? {
        id: (invoice as any).batch.id,
        code: (invoice as any).batch.code,
        name: (invoice as any).batch.name,
        type: (invoice as any).batch.freight_type || "SEA",
      }
    : undefined;

  return {
    invoice: {
      ...invoice,
      terms_and_conditions,
      notes,
    },
    customer: invoice.customer
      ? {
          id: invoice.customer.id,
          name: invoice.customer.name,
          email: invoice.customer.email || "",
          phone: invoice.customer.phone || undefined,
          location: invoice.customer.location || undefined,
        }
      : undefined,
    company: { ...DEFAULT_COMPANY_CONFIG, ...companyInfo },
    lineItems,
    totals,
    batch,
  };
}

/**
 * Interface for cargo image attachment data
 */
interface CargoImageAttachment {
  id: string;
  index: number;
  name: string;
  path: string;
  signedUrl: string;
  cargoId: string;
  trackingNumber: string;
  description?: string;
}

/**
 * Helper function to check if a file is an image based on extension
 */
function isImageFile(fileName: string): boolean {
  const imageExtensions = ["png", "jpeg", "jpg", "gif", "bmp", "webp"];
  const extension = fileName.split(".").pop()?.toLowerCase();
  return imageExtensions.includes(extension || "");
}

/**
 * Fetch cargo image attachments for invoice line items
 * Filters documents by associated_table='cargos' and image extensions
 * Returns O(n) efficiency by using query-level filtering
 */
async function fetchCargoImageAttachments(
  lineItems: GeneralInvoiceTemplateData["lineItems"]
): Promise<CargoImageAttachment[]> {
  console.log("Line Items (Details):", lineItems);

  try {
    const documentService = new DocumentService();
    const cargoImageAttachments: CargoImageAttachment[] = [];

    // Extract unique cargo IDs from line items
    const cargoIds = lineItems
      .map((item: any) => item.cargo_id)
      .filter((id: string) => id && id !== "N/A")
      .filter((id, index, array) => array.indexOf(id) === index); // Remove duplicates

    if (cargoIds.length === 0) {
      return [];
    }

    // Fetch documents for all cargo IDs in batch
    for (const cargoId of cargoIds) {
      const documentsResult = await documentService.getDocumentsByEntity(
        "cargos",
        cargoId,
        {
          limit: 100, // Reasonable limit for attachments per cargo
          filters: {
            status: "ACTIVE",
          },
        }
      );

      if (documentsResult.success && documentsResult.data) {
        // Filter for image files and get signed URLs
        const imageDocuments = documentsResult.data.filter((doc: any) =>
          isImageFile(doc.name)
        );

        let count: number = 0;
        for (const doc of imageDocuments) {
          try {
            const signedUrlResult =
              await documentService.getDocumentDownloadUrl(doc.path);

            const getTrackingNumber = (cargoId: string): string =>
              lineItems.find((item: any) => item.cargo_id === cargoId)
                ?.tracking_number || "N/A";

            let trackingNumber = getTrackingNumber(cargoId);

            count += 1;

            if (signedUrlResult.success && signedUrlResult.data) {
              cargoImageAttachments.push({
                id: doc.id,
                index: count,
                trackingNumber: simplifyTrackingNumber(trackingNumber),
                name: doc.name,
                path: doc.path,
                signedUrl: signedUrlResult.data,
                cargoId: cargoId,
                description: doc.description || undefined,
              });
            }
          } catch (error) {
            console.warn(
              `Failed to get signed URL for document ${doc.id}:`,
              error
            );
          }
        }
      }
    }

    return cargoImageAttachments;
  } catch (error) {
    console.error("Error fetching cargo image attachments:", error);
    return [];
  }
}

/**
 * Render cargo image attachments as PDF content elements
 * Creates inline layout with 50px dimensions and gap-4 spacing
 */
function renderCargoImageAttachments(
  attachments: CargoImageAttachment[]
): any[] {
  if (attachments.length === 0) {
    return [];
  }

  const pdfElements: any[] = [];

  // Add section header
  pdfElements.push({
    type: "heading",
    value: "CARGO IMAGE ATTACHMENTS",
    style: {
      fontSize: 12,
      bold: true,
      marginBottom: 8,
      color: "#1f2937",
    },
  });

  // Group attachments by cargo ID for better organization
  const attachmentsByCargoId = attachments.reduce(
    (acc, attachment) => {
      const cargoId = attachment.cargoId;
      if (!acc[cargoId]) {
        acc[cargoId] = [];
      }
      acc[cargoId]!.push(attachment);
      return acc;
    },
    {} as Record<string, CargoImageAttachment[]>
  );

  // Render attachments for each cargo
  Object.entries(attachmentsByCargoId).forEach(
    ([_, cargoAttachments], index) => {
      let getFileType = (name: string): string => {
        return name.split(".").pop()?.toUpperCase() || "PNG";
      };

      // Add individual images with proper sizing
      cargoAttachments.forEach((attachment) => {
        pdfElements.push({
          type: "paragraph",
          value: `${index + 1}.${attachment.index}: Tracking Number - ${attachment.trackingNumber}`,
          style: {
            fontSize: 8,
            marginBottom: 2,
            color: "#64748b",
          },
        });

        pdfElements.push({
          type: "image",
          value: {
            url: attachment.signedUrl,
            type: getFileType(attachment.name),
            width: 40,
            height: 30,
          },
          style: {
            marginBottom: 3,
          },
        });
      });

      // Add spacing between cargo groups
      pdfElements.push({
        type: "paragraph",
        value: "",
        style: {
          marginBottom: 12,
        },
      });
    }
  );

  return pdfElements;
}

/**
 * Generate general invoice document
 */
export async function generateGeneralInvoiceDocument(
  templateData: GeneralInvoiceTemplateData
): Promise<{
  success: boolean;
  data?: GeneratedInvoiceDocument;
  error?: string;
}> {
  try {
    const documentNumber = `INV-${templateData.invoice.inv_number || templateData.invoice.id}`;
    const fileName = `${documentNumber}.pdf`;

    // Build PDF content for general invoice
    const pdfContent: JSONDocumentData = {
      title: `Invoice ${templateData.invoice.inv_number}`,
      metadata: {
        author: templateData.company.name,
        subject: `Invoice ${templateData.invoice.inv_number}`,
        keywords: "invoice,billing,shamwaa,logistics",
        creator: "(CRM) Shamwaa Africa",
      },
      content: [
        // Header with company info
        {
          type: "heading",
          value: templateData.company.name,
          style: {
            fontSize: 20,
            bold: true,
            alignment: "center",
            color: "#1e40af",
          },
        },
        {
          type: "paragraph",
          value: `${templateData.company.address}\nPhone: ${templateData.company.phone} | Email: ${templateData.company.email}`,
          style: {
            fontSize: 9,
            alignment: "center",
            color: "#64748b",
          },
        },

        // Invoice header
        {
          type: "heading",
          value: "INVOICE",
          style: {
            fontSize: 16,
            bold: true,
            alignment: "center",
          },
        },
        {
          type: "paragraph",
          value: `Invoice #: ${templateData.invoice.inv_number}\nDate: ${new Date(templateData.invoice.created_at).toLocaleDateString()}${templateData.invoice.due_at ? `\nDue Date: ${new Date(templateData.invoice.due_at).toLocaleDateString()}` : ""}`,
          style: {
            fontSize: 10,
            alignment: "center",
          },
        },

        // Customer information
        {
          type: "heading",
          value: "Bill To:",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: `${templateData.customer?.name || "Customer"}${templateData.customer?.email ? `\nEmail: ${templateData.customer.email}` : ""}${templateData.customer?.phone ? `\nPhone: ${templateData.customer.phone}` : ""}${templateData.invoice.billing_address ? `\n${templateData.invoice.billing_address}` : ""}`,
        },

        // Line items table
        {
          type: "heading",
          value: "Items",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "table",
          value: {
            head: ["Description", "Quantity", "Rate", "Total"],
            body: templateData.lineItems.map((item) => [
              item.description,
              item.quantity.toString(),
              formatCurrency(item.unitPrice),
              formatCurrency(item.total),
            ]),
          },
          style: {
            fontSize: 10,
          },
        },

        // Totals
        {
          type: "paragraph",
          value: `Subtotal: ${formatCurrency(templateData.totals.subtotal)}${templateData.totals.discount ? `\nDiscount: -${formatCurrency(templateData.totals.discount)}` : ""}\n\nTotal: ${formatCurrency(templateData.totals.total)}`,
          style: {
            fontSize: 12,
            bold: true,
            alignment: "right",
          },
        },
      ],
    };

    // Add terms and conditions if provided
    if (templateData.invoice.terms_and_conditions) {
      pdfContent.content.push(
        {
          type: "heading",
          value: "Terms and Conditions",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: templateData.invoice.terms_and_conditions,
        }
      );
    }

    // Add notes if provided
    if (templateData.invoice.notes) {
      pdfContent.content.push(
        {
          type: "heading",
          value: "Notes",
          style: {
            fontSize: 12,
            bold: true,
          },
        },
        {
          type: "paragraph",
          value: templateData.invoice.notes,
        }
      );
    }

    // Mock document generation result
    const document: GeneratedInvoiceDocument = {
      url: `/api/documents/invoices/${fileName}`,
      fileName,
      documentNumber,
      metadata: {
        invoiceId: templateData.invoice.id,
        customerId: templateData.customer?.id,
        generatedAt: new Date().toISOString(),
        templateVersion: "1.0.0",
      },
    };

    return {
      success: true,
      data: document,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to generate invoice document",
    };
  }
}

/**
 * Generate invoice template as PDF blob
 *
 * @param templateData - Prepared invoice template data
 * @returns Promise<Blob> - PDF blob ready for download
 */
export async function generateInvoiceTemplate(
  templateData: GeneralInvoiceTemplateData
): Promise<Blob> {
  // PDF Styling Constants
  const PDF_STYLES = {
    colors: {
      primary: "#1f2937",
      secondary: "#374151",
      muted: "#64748b",
      accent: "#3b82f6",
    },
    fonts: {
      small: 7,
      normal: 8,
      medium: 10,
      large: 12,
      xlarge: 14,
      xxlarge: 17,
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
      xxl: 24,
      xxxl: 32,
    },
  };

  const cargoImageAttachments = await fetchCargoImageAttachments(
    templateData.lineItems
  );

  // Helper function to build company header section
  const buildCompanyHeader = () => [
    {
      type: "image" as const,
      value: {
        url: templateData.company.logo,
        width: 60,
        height: 24,
      },
      style: {
        alignment: "center" as const,
        marginBottom: PDF_STYLES.spacing.md,
      },
    },
    {
      type: "paragraph" as const,
      value: `${templateData.company.address}\nPhone: ${templateData.company.phone} | Email: ${templateData.company.email}`,
      style: {
        fontSize: PDF_STYLES.fonts.normal,
        alignment: "center" as const,
        color: PDF_STYLES.colors.muted,
        marginBottom: PDF_STYLES.spacing.xl,
      },
    },
  ];

  // Helper function to build invoice header section
  const buildInvoiceHeader = () => [
    {
      type: "paragraph" as const,
      value: `${
        templateData.batch
          ? `\nBatch: ${templateData.batch.code} | Type: ${templateData.batch.type}`
          : ""
      }\nIssue Date: ${new Date(templateData.invoice.created_at).toLocaleDateString()}`,
      style: {
        fontSize: PDF_STYLES.fonts.normal,
        marginBottom: PDF_STYLES.spacing.xl,
        alignment: "center" as const,
        color: PDF_STYLES.colors.secondary,
      },
    },
  ];

  // Helper function to build customer information section
  const buildCustomerInfo = () => {
    if (!templateData.customer) {
      return [];
    }

    const customerDetails = [
      templateData.customer.name,
      templateData.customer.email,
      templateData.customer.phone,
      templateData.customer.location,
    ]
      .filter(Boolean)
      .join("\n");

    return [
      {
        type: "heading" as const,
        value: "Bill To:",
        style: {
          fontSize: PDF_STYLES.fonts.large,
          bold: true,
          marginBottom: PDF_STYLES.spacing.sm,
          color: PDF_STYLES.colors.primary,
        },
      },
      {
        type: "paragraph" as const,
        value: customerDetails,
        style: {
          fontSize: PDF_STYLES.fonts.medium,
          marginBottom: PDF_STYLES.spacing.xxl,
        },
      },
    ];
  };

  const TableHeadList: string[] = [
    "Tracking Number",
    "China #",
    "Description",
    "CTN",
    "Unit Price",
    "Total",
  ];

  let batchType: string = templateData.batch?.type || "SEA";
  let metric: string = batchType === "AIR" ? "Weight" : "CBM";
  TableHeadList.splice(3, 0, metric);

  // Helper function to build line items table
  const buildLineItemsTable = () => [
    {
      type: "heading" as const,
      value: "Invoice Items",
      style: {
        fontSize: PDF_STYLES.fonts.xlarge,
        bold: true,
        marginBottom: PDF_STYLES.spacing.md,
        color: PDF_STYLES.colors.primary,
      },
    },
    {
      type: "table" as const,
      value: {
        head: TableHeadList,
        body: templateData.lineItems.map((item: any) => {
          let metricValue: string =
            batchType === "AIR"
              ? `${item.weight_value} ${item.weight_unit}`
              : `${item.cbm_value} ${item.cbm_unit}`;

          return [
            simplifyTrackingNumber(item.tracking_number),
            item.china_tracking_number,
            item.description,
            metricValue,
            item.ctn,
            item.unitPrice,
            item.total,
          ];
        }),
      },
      style: {
        fontSize: PDF_STYLES.fonts.normal,
        marginBottom: PDF_STYLES.spacing.xl,
      },
    },
    {
      type: "paragraph" as const,
      value: `\nInvoice Total (USD): $${templateData.totals.totalPrice.toLocaleString()}\nInvoice Total (TZS): ${Math.round(templateData.totals.totalPrice * (templateData.invoice.currency_conv_rate || 2622)).toLocaleString()}`,
      style: {
        fontSize: PDF_STYLES.fonts.large,
        bold: true,
        alignment: "right" as const,
        marginBottom: PDF_STYLES.spacing.xxxl,
        color: PDF_STYLES.colors.primary,
      },
    },
  ];

  // Helper function to build bank information section
  const buildBankInfo = (batchInfo?: { type: string }) => {
    // Determine TZS account number based on freight type
    const isAirFreight = batchInfo?.type?.toUpperCase() === "AIR";
    const tzsAccountNumber = isAirFreight ? "015C667508301" : "015C667508300";

    return [
      {
        type: "heading" as const,
        value: "Payment Information",
        style: {
          fontSize: PDF_STYLES.fonts.large,
          bold: true,
          marginBottom: PDF_STYLES.spacing.md,
          color: PDF_STYLES.colors.primary,
        },
      },
      {
        type: "paragraph" as const,
        value: `Beneficiary Bank: CRDB BANK PLC\nBranch: TABATA BRANCH\nSwift Code: CORUTZTZXXX\n\nUSD Account: 025C667508300\nTZS Account: ${tzsAccountNumber}\nAccount Name: SHAMWAA TRADING CO. LTD`,
        style: {
          fontSize: PDF_STYLES.fonts.normal,
          alignment: "left" as const,
          marginBottom: PDF_STYLES.spacing.xl,
          color: PDF_STYLES.colors.secondary,
        },
      },
    ];
  };

  // Helper function to build terms and conditions section
  const buildTermsAndConditions = () => {
    if (!templateData.invoice.terms_and_conditions) return [];
    return [
      {
        type: "heading" as const,
        value: "Terms and Conditions",
        style: {
          fontSize: PDF_STYLES.fonts.medium,
          bold: true,
          color: PDF_STYLES.colors.primary,
          marginBottom: PDF_STYLES.spacing.sm,
        },
      },
      {
        type: "paragraph" as const,
        value: templateData.invoice.terms_and_conditions,
        style: {
          fontSize: PDF_STYLES.fonts.small,
          color: PDF_STYLES.colors.muted,
          marginBottom: PDF_STYLES.spacing.lg,
        },
      },
    ];
  };

  // Helper function to build notes section
  const buildNotesSection = () => {
    if (!templateData.invoice.notes) return [];
    return [
      {
        type: "heading" as const,
        value: "Additional Notes",
        style: {
          fontSize: PDF_STYLES.fonts.medium,
          bold: true,
          color: PDF_STYLES.colors.primary,
          marginBottom: PDF_STYLES.spacing.sm,
        },
      },
      {
        type: "paragraph" as const,
        value: templateData.invoice.notes,
        style: {
          fontSize: PDF_STYLES.fonts.normal,
          color: PDF_STYLES.colors.secondary,
          marginBottom: PDF_STYLES.spacing.lg,
        },
      },
    ];
  };

  // Build complete PDF content with organized sections
  const pdfContent: JSONDocumentData = {
    title: `Invoice: ${templateData?.batch?.name} - ${templateData?.batch?.type}`,
    metadata: {
      author: templateData.company.name,
      subject: `Invoice ${templateData.invoice.inv_number}`,
      keywords: "invoice,billing,shamwaa,logistics,payment",
      creator: "Shamwaa Logistics System",
    },
    content: [
      ...buildCompanyHeader(),
      ...buildInvoiceHeader(),
      ...buildCustomerInfo(),
      ...buildLineItemsTable(),
      ...buildBankInfo(templateData.batch),
      ...buildTermsAndConditions(),
      ...buildNotesSection(),
      ...renderCargoImageAttachments(cargoImageAttachments),
    ],
  };

  // Enhanced PDF generation options with styling constants
  const pdfOptions: PDFOptions = {
    format: "a4",
    orientation: "portrait",
    fontName: "helvetica",
    fontSize: PDF_STYLES.fonts.normal,
    margins: {
      top: PDF_STYLES.spacing.xxxl,
      right: PDF_STYLES.spacing.xl,
      bottom: PDF_STYLES.spacing.xxxl,
      left: PDF_STYLES.spacing.xl,
    },
    showHeader: true,
    headerText: `${templateData.company.name} - Professional Invoice`,
    showFooter: true,
    footerText: `Invoice: ${templateData.invoice.inv_number} | Generated: ${new Date().toLocaleDateString()} | Page`,
    includePageNumbers: true,
  };

  // Generate and return PDF blob
  return await generatePDFFromJSON(pdfContent, pdfOptions);
}
