import {
  type CargoWithRelations,
  Cargo_Categories,
  CARGO_CATEGORY_LABELS,
} from "@/lib/logistics";
import { type CargoDisplay } from "./types";
import { getDimensionUnitAbbreviation } from "@/lib/utils/unit-mappings";

// Re-export types
export type * from "./types";

// Helper function to map cargo category
export const getCargoCategory = (category: string | null): string => {
  switch (category?.toUpperCase()) {
    case Cargo_Categories.DANGEROUS:
      return CARGO_CATEGORY_LABELS[Cargo_Categories.DANGEROUS];
    case Cargo_Categories.SAFE:
    case "SAFE": // Handle legacy "SAFE" value
      return CARGO_CATEGORY_LABELS[Cargo_Categories.SAFE];
    default:
      return "Unclassified";
  }
};

// Helper function to format weight
export const formatWeight = (
  weight: number | null,
  unit: string | null
): string => {
  if (!weight) return "N/A";
  return `${weight.toLocaleString()} ${unit || "kg"}`;
};

// Helper function to format CBM
export const formatCBM = (cbm: number | null, unit: string | null): string => {
  if (!cbm) return "N/A";
  return `${cbm.toFixed(4)} ${unit || "m³"}`;
};

// Helper function to format date
export const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString();
};

// Helper function to get entity name with default fallback
export const getEntityName = (cargo: CargoWithRelations): string => {
  if (cargo.customers?.name) {
    return cargo.customers.name;
  }
  if ((cargo as any).suppliers?.tracking_number) {
    return `Supplier: ${(cargo as any).suppliers.tracking_number}`;
  }
  if ((cargo as any).suppliers?.phone) {
    return `Supplier: ${(cargo as any).suppliers.phone}`;
  }
  if ((cargo as any).suppliers?.location) {
    return `Supplier: ${(cargo as any).suppliers.location}`;
  }
  return "N/A";
};

// Helper function to get entity type
export const getEntityType = (
  cargo: CargoWithRelations
): "customer" | "supplier" | "unknown" => {
  if (cargo.customers) return "customer";
  if ((cargo as any).suppliers) return "supplier";
  return "unknown";
};

// Helper function to get entity ID for linking
export const getEntityId = (cargo: CargoWithRelations): string => {
  if (cargo.customer_id) return cargo.customer_id;
  if ((cargo as any).supplier_id) return (cargo as any).supplier_id;
  return "";
};

// Helper function to get entity phone number
export const getEntityPhone = (cargo: CargoWithRelations): string => {
  if (cargo.customers?.phone) {
    return cargo.customers.phone;
  }
  if ((cargo as any).suppliers?.phone) {
    return (cargo as any).suppliers.phone;
  }
  return "";
};

// Helper function to format dimensions
const formatDimensionsDisplay = (cargo: CargoWithRelations): string => {
  if (
    !cargo.dimension_length ||
    !cargo.dimension_width ||
    !cargo.dimension_height
  ) {
    return "N/A";
  }
  const unit =
    cargo.dimension_unit === "METER_CUBIC"
      ? "m"
      : cargo.dimension_unit === "FEET_CUBIC"
        ? "ft"
        : "cm";
  return `${cargo.dimension_length} × ${cargo.dimension_width} × ${cargo.dimension_height} ${unit}`;
};

// Helper function to format price
const formatPrice = (price: number | null): string => {
  if (!price) return "N/A";
  return `$${price.toLocaleString()}`;
};

// Helper function to transform cargo data for display
export const transformCargoData = (
  cargos: CargoWithRelations[]
): CargoDisplay[] => {
  return cargos.map((cargo) => ({
    id: cargo.id,
    trackingNumber: cargo.tracking_number || "N/A",
    chinaTrackingNumber: cargo.china_tracking_number || "N/A",
    type: cargo.type.toLowerCase(),
    origin: cargo.batches?.freights?.origin || "N/A",
    destination: cargo.batches?.freights?.destination || "N/A",
    status: cargo.status || "Unknown",
    weight: cargo.weight_value ? `${cargo.weight_value} kg` : "N/A",
    cbm: cargo.cbm_value
      ? `${cargo.cbm_value.toFixed(2)} ${getDimensionUnitAbbreviation(cargo.cbm_unit)}`
      : "N/A",
    customer: getEntityName(cargo),
    customerId: getEntityId(cargo),
    customerPhone: getEntityPhone(cargo),
    entityType: getEntityType(cargo),
    date: cargo.created_at ? formatDate(cargo.created_at) : "N/A",
    updatedAt: cargo.updated_at ? formatDate(cargo.updated_at) : "N/A",
    particular: cargo.particular || "N/A",
    batchCode: cargo.batches?.code || "N/A",
    batchId: cargo.batch_id || "",
    ctn: cargo.ctn || 0,
    // New enhanced fields
    dimensions: formatDimensionsDisplay(cargo),
    unitPrice: formatPrice(cargo.unit_price),
    totalPrice: formatPrice(cargo.total_price),
    invoiceStatus: (cargo as any).invoices?.status || "No Invoice",
    customDeclaration: (cargo as any).custom_declaration || false,
    // Assigned user information
    assignedTo: (cargo as any).users?.name.toLowerCase() || "Unassigned",
    assignedToId: cargo.assigned_to || "",
  }));
};
