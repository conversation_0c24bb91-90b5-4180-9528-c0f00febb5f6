"use client";

import { redirect } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import { useAppSelector } from "@/store/hooks";
import { cargoService, batchService } from "@/lib/logistics";
import { invoiceService } from "@/lib/logistics/operations/invoices";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import { type CargoManagementState, type CargoDisplay } from "../utils/types";
import { transformCargoData } from "../utils";
import { toast } from "sonner";

/**
 * Custom hook for cargo management state and business logic
 *
 * This hook encapsulates all the state management and business logic
 * for the cargo management page, following React best practices.
 */
export function useCargoManagement() {
  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // State management
  const [state, setState] = useState<CargoManagementState>({
    viewMode: "table",
    searchTerm: "",
    categoryFilter: "all",
    currentPage: 1,
    loading: true,
    refreshing: false,
    cargoData: [],
    cargoDataCount: 0,
    cargoStats: {
      totalCargos: 0,
      inTransit: 0,
      delivered: 0,
      issues: 0,
      totalValue: 0,
      totalCBM: 0,
      totalCTN: 0,
      totalWeight: 0,
      totalCustomers: 0,
    },
    isNewCargoOpen: false,
    qrCodeDialog: {
      open: false,
      cargo: null,
    },
    cargoDetailsDialog: {
      open: false,
      cargo: null,
    },
    columnFilters: [],
    filters: {
      cargoType: "all",
      status: "all",
      dateFrom: "",
      dateTo: "",
    },
    selectedCargos: new Set(),
    bulkActionLoading: false,
    bulkStatusUpdate: "",
    updatingStatuses: {},
    demoMode: false,
    exportDialog: {
      isOpen: false,
      data: [],
      config: {
        title: "Cargo Export",
        filename: `cargo_export_${new Date().toISOString().split("T")[0]}`,
        excludeColumns: ["id"],
        columnMapping: {
          trackingNumber: "Tracking Number",
          chinaTrackingNumber: "China Tracking Number",
          particular: "Description",
          unitPrice: "Unit Price",
          totalPrice: "Total Price",
          invoiceStatus: "Invoice Status",
          updatedAt: "Last Updated",
          batchCode: "Batch Code",
          assignedTo: "Assigned To",
        },
      },
    },
    isBulkTaskDialogOpen: false,
  });

  // Update state helper
  const updateState = useCallback((updates: Partial<CargoManagementState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Fetch cargo data
  const fetchCargoData = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) updateState({ refreshing: true });
        else updateState({ loading: true });

        // Determine if we should use search or regular fetch
        const hasSearchTerm =
          state.searchTerm && state.searchTerm.trim().length > 0;

        let result;
        if (hasSearchTerm) {
          // Use search function from cargo service
          result = await cargoService.searchCargos(state.searchTerm.trim(), {
            column: "created_at",
            ascending: false,
            filters: {
              // Apply any additional filters from state
              ...(state.filters.status !== "all" && {
                status: state.filters.status as any,
              }),
              ...(state.filters.cargoType !== "all" && {
                cargo_type: state.filters.cargoType as any,
              }),
            },
          });
        } else {
          // Use regular fetch with BaseService pagination
          result = await cargoService.getAllCargosWithRelations({
            column: "created_at",
            ascending: false,
            filters: {
              // Apply filters from state
              ...(state.filters.status !== "all" && {
                status: state.filters.status as any,
              }),
              ...(state.filters.cargoType !== "all" && {
                cargo_type: state.filters.cargoType as any,
              }),
            },
          });
        }

        if (result.success) {
          const transformedData = transformCargoData(result.data);
          updateState({
            cargoData: transformedData,
            cargoDataCount: result.count || 0,
            currentPage: cargoService.currentPage,
          });
        } else {
          console.error("Failed to fetch cargo data:", result.error);
        }
      } catch (error) {
        console.error("Error fetching cargo data:", error);
      } finally {
        updateState({ loading: false, refreshing: false });
      }
    },
    [
      authUser,
      updateState,
      state.searchTerm,
      state.filters.status,
      state.filters.cargoType,
    ]
  );

  const fetchCargoStats = useCallback(async () => {
    // Fetch cargo statistics
    const statsResult = await cargoService.getCargoStats();
    if (statsResult.success && statsResult.data) {
      const stats = statsResult.data;
      const statusCounts = stats.cargosByStatus;

      // Use stats directly from cargoService.getCargoStats()
      updateState({
        cargoStats: {
          totalCargos: stats.totalCargos,
          inTransit:
            statusCounts.find((s: any) =>
              ["IN_TRANSIT", "PROCESSING"].includes(s.status)
            )?.count || 0,
          delivered:
            statusCounts.find((s: any) => s.status === "COMPLETED")?.count || 0,
          issues:
            statusCounts.find((s: any) =>
              ["CANCELLED", "DELAYED"].includes(s.status)
            )?.count || 0,
          totalValue: stats.totalValue || 0,
          totalCBM: stats.totalCBM || 0,
          totalCTN: stats.totalCTN || 0,
          totalWeight: stats.totalWeight || 0,
          totalCustomers: stats.totalCustomers || 0,
        },
      });
    }
  }, [updateState]);

  // Initial fetch
  useEffect(() => {
    if (authUser) {
      fetchCargoData();
      fetchCargoStats();
    }
  }, [authUser, fetchCargoData]);

  // Trigger search when search term or filters change
  useEffect(() => {
    if (authUser) {
      // Reset to first page when search term or filters change
      cargoService.currentPage = 1;
      updateState({ currentPage: 1 });
      fetchCargoData();
    }
  }, [state.searchTerm, state.filters.status, state.filters.cargoType]);

  // Reset pagination when filters change
  useEffect(() => {
    updateState({ currentPage: 1 });
  }, [
    state.searchTerm,
    state.categoryFilter,
    state.columnFilters,
    updateState,
  ]);

  // Pagination state and methods using BaseService pagination
  const pagination = {
    currentPage: cargoService.currentPage,
    totalPages: cargoService.totalPages,
    totalItems: cargoService.totalItems,
    itemsPerPage: cargoService.itemsPerPage,

    // Navigation methods
    nextPage: useCallback(() => {
      cargoService.nextPage();
      updateState({ currentPage: cargoService.currentPage });
      fetchCargoData(true); // Refresh data for new page
    }, [updateState, fetchCargoData]),

    previousPage: useCallback(() => {
      cargoService.previousPage();
      updateState({ currentPage: cargoService.currentPage });
      fetchCargoData(true); // Refresh data for new page
    }, [updateState, fetchCargoData]),

    firstPage: useCallback(() => {
      cargoService.firstPage();
      updateState({ currentPage: cargoService.currentPage });
      fetchCargoData(true); // Refresh data for new page
    }, [updateState, fetchCargoData]),

    lastPage: useCallback(() => {
      cargoService.lastPage();
      updateState({ currentPage: cargoService.currentPage });
      fetchCargoData(true); // Refresh data for new page
    }, [updateState, fetchCargoData]),

    goToPage: useCallback(
      (page: number) => {
        cargoService.goToPage(page);
        updateState({ currentPage: page });
        fetchCargoData(true); // Refresh data for new page
      },
      [updateState, fetchCargoData]
    ),

    setItemsPerPage: useCallback(
      (itemsPerPage: number) => {
        cargoService.itemsPerPage = itemsPerPage;
        fetchCargoData(true); // Refresh data for new page
      },
      [updateState, fetchCargoData]
    ),
  };

  // Refresh handler
  const handleRefresh = useCallback(() => {
    fetchCargoData(true);
  }, [fetchCargoData]);

  // Delete cargo handler
  const handleDeleteCargo = useCallback(
    async (cargo: CargoDisplay) => {
      const confirmed = window.confirm(
        `Are you sure you want to delete cargo ${cargo.trackingNumber}? This action cannot be undone.`
      );

      if (!confirmed) return;

      try {
        updateState({
          updatingStatuses: {
            ...state.updatingStatuses,
            [cargo.id]: true,
          },
        });

        const result = await cargoService.deleteCargoWithInvoiceUpdates(
          cargo.id
        );

        if (result.success) {
          toast.success("Cargo deleted successfully", {
            description: "Associated invoices have been updated automatically.",
          });
          fetchCargoData(true); // Refresh data
        } else {
          toast.error("Failed to delete cargo", {
            description: result.error || "Please try again.",
          });
        }
      } catch (error) {
        console.error("Error deleting cargo:", error);
        toast.error("Failed to delete cargo", {
          description: "An unexpected error occurred.",
        });
      } finally {
        updateState({
          updatingStatuses: {
            ...state.updatingStatuses,
            [cargo.id]: false,
          },
        });
      }
    },
    [state.updatingStatuses, updateState, fetchCargoData]
  );

  // Individual cargo assignment handlers
  const handleAssignCargoToBatch = useCallback(
    async (
      cargoId: string,
      batchId: string,
      options?: {
        skipConfirmation?: boolean;
        onSuccess?: () => void;
        onError?: (error: string) => void;
      }
    ) => {
      const { skipConfirmation = false, onSuccess, onError } = options || {};

      if (!skipConfirmation) {
        const confirmed = window.confirm(
          "Are you sure you want to assign this cargo to the selected batch?"
        );
        if (!confirmed) return;
      }

      try {
        updateState({
          updatingStatuses: {
            ...state.updatingStatuses,
            [cargoId]: true,
          },
        });

        const result = await cargoService.assignToBatch(cargoId, batchId);

        if (result.success) {
          toast.success("Cargo assigned to batch successfully", {
            description:
              "The cargo has been successfully assigned to the batch.",
          });

          // Refresh data to show updated assignments
          fetchCargoData(true);

          // Call success callback if provided
          if (onSuccess) {
            onSuccess();
          }
        } else {
          const errorMessage =
            result.error || "Failed to assign cargo to batch";
          toast.error("Failed to assign cargo", {
            description: errorMessage,
          });

          // Call error callback if provided
          if (onError) {
            onError(errorMessage);
          }
        }
      } catch (error) {
        console.error("Error assigning cargo to batch:", error);
        const errorMessage =
          "An unexpected error occurred while assigning cargo";
        toast.error("Failed to assign cargo", {
          description: errorMessage,
        });

        // Call error callback if provided
        if (onError) {
          onError(errorMessage);
        }
      } finally {
        updateState({
          updatingStatuses: {
            ...state.updatingStatuses,
            [cargoId]: false,
          },
        });
      }
    },
    [state.updatingStatuses, updateState, fetchCargoData]
  );

  const handleUnassignCargoFromBatch = useCallback(
    async (
      cargoId: string,
      options?: {
        skipConfirmation?: boolean;
        onSuccess?: () => void;
        onError?: (error: string) => void;
      }
    ) => {
      const { skipConfirmation = false, onSuccess, onError } = options || {};

      if (!skipConfirmation) {
        const confirmed = window.confirm(
          "Are you sure you want to unassign this cargo from its batch?"
        );
        if (!confirmed) return;
      }

      try {
        updateState({
          updatingStatuses: {
            ...state.updatingStatuses,
            [cargoId]: true,
          },
        });

        const result = await cargoService.removeFromBatch(cargoId);

        if (result.success) {
          toast.success("Cargo unassigned from batch successfully", {
            description:
              "The cargo has been successfully unassigned from its batch.",
          });

          // Refresh data to show updated assignments
          fetchCargoData(true);

          // Call success callback if provided
          if (onSuccess) {
            onSuccess();
          }
        } else {
          const errorMessage =
            result.error || "Failed to unassign cargo from batch";
          toast.error("Failed to unassign cargo", {
            description: errorMessage,
          });

          // Call error callback if provided
          if (onError) {
            onError(errorMessage);
          }
        }
      } catch (error) {
        console.error("Error unassigning cargo from batch:", error);
        const errorMessage =
          "An unexpected error occurred while unassigning cargo";
        toast.error("Failed to unassign cargo", {
          description: errorMessage,
        });

        // Call error callback if provided
        if (onError) {
          onError(errorMessage);
        }
      } finally {
        updateState({
          updatingStatuses: {
            ...state.updatingStatuses,
            [cargoId]: false,
          },
        });
      }
    },
    [state.updatingStatuses, updateState, fetchCargoData]
  );

  // Cargo action handlers
  const handleCargoAction = useCallback(
    (action: string, cargo?: CargoDisplay, actionData?: any) => {
      switch (action) {
        case "create":
          updateState({ isNewCargoOpen: true });
          break;
        case "qr-code":
          if (cargo) {
            updateState({
              qrCodeDialog: { open: true, cargo },
            });
          }
          break;
        case "batch":
          if (actionData.batchId)
            redirect(`/batch-management/${actionData.batchId}`);

          break;
        case "view":
          if (cargo) {
            // Navigate to cargo detail page or open view modal
            redirect(`/cargo-management/${cargo.id}`);
          }
          break;
        case "view-details":
          if (cargo) {
            updateState({
              cargoDetailsDialog: { open: true, cargo },
            });
          }
          break;
        case "edit":
          if (cargo) {
            // TODO: Implement edit functionality
            toast.info("Edit functionality coming soon");
          }
          break;
        case "delete":
          if (cargo) {
            handleDeleteCargo(cargo);
          }
          break;
        case "assign":
          if (cargo && actionData?.batchId) {
            handleAssignCargoToBatch(
              cargo.id,
              actionData.batchId,
              actionData.options
            );
          }
          break;
        case "unassign":
          if (cargo) {
            handleUnassignCargoFromBatch(cargo.id, actionData?.options);
          }
          break;
        default:
          break;
      }
    },
    [
      updateState,
      handleDeleteCargo,
      handleAssignCargoToBatch,
      handleUnassignCargoFromBatch,
    ]
  );

  // Dialog close handlers
  const handleDialogClose = useCallback(
    (dialogType: string) => {
      switch (dialogType) {
        case "create":
          updateState({ isNewCargoOpen: false });
          break;
        case "qr-code":
          updateState({
            qrCodeDialog: { open: false, cargo: null },
          });
          break;
        case "cargo-details":
          updateState({
            cargoDetailsDialog: { open: false, cargo: null },
          });
          break;
        default:
          break;
      }
    },
    [updateState]
  );

  // Cargo mutation handler (for create/update/delete)
  const handleCargoMutation = useCallback(() => {
    fetchCargoData(true);
  }, [fetchCargoData]);

  // Bulk actions handlers
  const handleBulkStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedCargos);
      if (selectedIds.length === 0) {
        toast.error("Please select cargo items to update");
        return;
      }

      try {
        updateState({ bulkActionLoading: true });

        // Update each selected cargo's status
        const updatePromises = selectedIds.map((cargoId) =>
          cargoService.update(cargoId, {
            status: status as any,
          })
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;
        const failedUpdates = updateResults.length - successfulUpdates;

        if (successfulUpdates > 0) {
          toast.success(
            `Successfully updated ${successfulUpdates} cargo(s) to ${status}`,
            {
              description:
                failedUpdates > 0
                  ? `${failedUpdates} update(s) failed`
                  : undefined,
            }
          );

          // Clear selection after successful action
          updateState({ selectedCargos: new Set() });
        } else {
          toast.error("Failed to update any cargo status");
        }
      } catch (error) {
        console.error("Error in bulk status update:", error);
        toast.error("Failed to update cargo status");
      } finally {
        updateState({ bulkActionLoading: false });
      }
    },
    [state.selectedCargos, updateState]
  );

  const handleBulkDelete = useCallback(() => {
    const selectedIds = Array.from(state.selectedCargos);
    if (selectedIds.length === 0) return;

    console.log(`Deleting ${selectedIds.length} cargo(s):`, selectedIds);
    // TODO: Implement bulk delete logic
    updateState({ selectedCargos: new Set() }); // Clear selection after action
  }, [state.selectedCargos, updateState]);

  const handleBulkCreateInvoice = useCallback(async () => {
    const selectedIds = Array.from(state.selectedCargos);

    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to create invoices from batch");
      return;
    }

    try {
      updateState({ bulkActionLoading: true });

      // Get the original cargo data with full details
      const selectedCargoIds = Array.from(selectedIds);
      const cargoDetailsPromises = selectedCargoIds.map((id) =>
        cargoService.getById(id, "*, customers (name), batches (id, code)")
      );

      const cargoDetailsResults = await Promise.all(cargoDetailsPromises);
      const selectedCargoDetails = cargoDetailsResults
        .filter((result: any) => result.success && result.data)
        .map((result: any) => result.data!);

      if (selectedCargoDetails.length === 0) {
        toast.error("Could not retrieve cargo details");
        return;
      }

      // Check if all selected cargos belong to the same customer and batch
      const firstCargo = selectedCargoDetails[0];
      if (!firstCargo) {
        toast.error("No cargo details found");
        return;
      }

      // Validate batch consistency first
      const batchGroups = new Map<
        string,
        { batchCode: string; count: number; cargos: string[] }
      >();

      for (const cargo of selectedCargoDetails) {
        const batchId = cargo.batch_id;
        if (!batchId) {
          toast.error("Some selected cargos do not have a batch assigned");
          return;
        }

        // Get batch code from the display data or cargo data
        const displayCargo = state.cargoData.find((d) => d.id === cargo.id);
        const batchCode =
          cargo.batches?.code || displayCargo?.batchCode || "Unknown Batch";

        if (!batchGroups.has(batchId)) {
          batchGroups.set(batchId, {
            batchCode: batchCode,
            count: 0,
            cargos: [],
          });
        }

        const group = batchGroups.get(batchId)!;
        group.count++;
        group.cargos.push(cargo.tracking_number || cargo.id);
      }

      // Check if there are multiple batches
      if (batchGroups.size > 1) {
        const batchList = Array.from(batchGroups.entries())
          .map(
            ([, info]) =>
              `• ${info.batchCode}: ${info.count} cargo(s) (${info.cargos.join(", ")})`
          )
          .join("\n");

        toast.error(
          `Cannot create invoice with cargos from different batches:\n\n${batchList}\n\nPlease select cargos from only one batch.`,
          {
            duration: 8000, // Longer duration for detailed message
          }
        );
        return;
      }

      console.log("\nDebugging (Cargo) Details: ", batchGroups);

      // Get the common batch_id and code for the invoice
      const commonBatchId: any = Array.from(batchGroups.keys())[0];
      const commonBatchCode =
        batchGroups.get(commonBatchId)?.batchCode || "Unknown Batch";

      // Validate customer consistency and provide detailed feedback
      const customerGroups = new Map<
        string,
        { name: string; count: number; cargos: string[] }
      >();

      for (const cargo of selectedCargoDetails) {
        const customerId = cargo.customer_id;
        if (!customerId) {
          toast.error("Some selected cargos do not have a customer assigned");
          return;
        }

        // Get customer name from the display data
        const displayCargo = state.cargoData.find((d) => d.id === cargo.id);
        const customerName =
          cargo.customers?.name || displayCargo?.customer || "Unknown Customer";

        if (!customerGroups.has(customerId)) {
          customerGroups.set(customerId, {
            name: customerName,
            count: 0,
            cargos: [],
          });
        }

        const group = customerGroups.get(customerId)!;
        group.count++;
        group.cargos.push(cargo.tracking_number || cargo.id);
      }

      // Check if there are multiple customers
      if (customerGroups.size > 1) {
        const customerList = Array.from(customerGroups.entries())
          .map(
            ([, info]) =>
              `• ${info.name}: ${info.count} cargo(s) (${info.cargos.join(", ")})`
          )
          .join("\n");

        toast.error(
          `Cannot create invoice with cargos from different customers:\n\n${customerList}\n\nPlease select cargos from only one customer.`,
          {
            duration: 8000, // Longer duration for detailed message
          }
        );
        return;
      }

      // Create line items from selected cargos
      const lineItems = selectedCargoDetails.map((cargo) => ({
        cargo_id: cargo.id, // Essential for linking line items back to cargos
        tracking_number: cargo.tracking_number,
        china_tracking_number: cargo.china_tracking_number,
        description: cargo.particular || `Cargo ${cargo.tracking_number}`,
        weight_value: cargo.weight_value || 0,
        weight_unit: cargo.weight_unit || "KILOGRAMS",
        cbm_value: cargo.cbm_value || 0,
        cbm_unit: cargo.cbm_unit || "METER_CUBIC",
        ctn: cargo.ctn || 0,
        quantity: cargo.quantity || 1, // Default quantity for cargo items
        unitPrice: Number(cargo.unit_price || 0),
        factor_value: cargo.factor_value,
        factor_unit: cargo.factor_unit,
        total: Number(cargo.total_price || 0),
      }));

      // Calculate totals
      const subtotal = lineItems.reduce((sum, item) => sum + item.total, 0);
      const total = subtotal;

      // Fetch existing invoice numbers for sequential generation
      const existingInvoicesResult = await invoiceService.getAll({
        filters: {},
        limit: 1000, // Get all to check invoice numbers
        offset: 0,
      });

      const existingInvoiceNumbers =
        existingInvoicesResult.success && existingInvoicesResult.data
          ? existingInvoicesResult.data
              .map((invoice) => invoice.inv_number)
              .filter((invNumber): invNumber is string => !!invNumber)
          : [];

      let customerName: string =
        customerGroups.get(firstCargo.customer_id)?.name || "Unknown Customer";

      // Fetch batch custom conversion rate if available
      let customConversionRate: number | null = null;
      if (commonBatchId) {
        try {
          const batchResult = await batchService.getById(commonBatchId);
          if (batchResult.success && batchResult.data) {
            customConversionRate = (batchResult.data as any).currency_conv_rate;
          }
        } catch (error) {
          // Use default conversion rate if batch fetch fails
        }
      }

      // Create single invoice with all selected cargos as line items
      const invoiceData = {
        inv_number: codeGeneratorService.generateSequentialInvoiceNumber(
          {
            batchCode: commonBatchCode || "UNKNOWN",
            customerName: customerName || "UNKNOWN",
          },
          existingInvoiceNumbers
        ),
        type: "AUTOMATED" as const,
        status: "PENDING" as const,
        customer_id: firstCargo.customer_id || null,
        batch_id: commonBatchId, // Add the validated common batch_id
        billing_address: "",
        due_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        terms_and_conditions:
          "Payment is due within 30 days of receipt of this invoice",
        notes: `Bulk invoice created for ${selectedIds.length} cargo(s) from batch ${commonBatchCode}: ${selectedCargoDetails
          .map((c) => c.tracking_number)
          .join(", ")}`,
        line_items: lineItems,
        subtotal,
        total,
        // Apply custom conversion rate from batch if available
        currency_conv_rate: customConversionRate,
      };

      const result = await invoiceService.createInvoice(invoiceData);

      if (result.success && result.data) {
        // Show success message with conversion rate info
        const rateMessage = customConversionRate
          ? `Using batch custom rate: 1 USD = ${customConversionRate.toLocaleString()} TZS`
          : "Using current market rate";

        toast.success(`Invoice created successfully! ${rateMessage}`, {
          description: `Invoice ${result.data.invoice.inv_number} created for ${selectedIds.length} cargo(s)`,
        });
        // Update cargo records with the newly created invoice ID
        try {
          const cargoUpdatePromises = Array.from(selectedIds).map((cargoId) =>
            cargoService.update(cargoId, {
              invoice_id: result.data!.invoice.id,
            })
          );

          const cargoUpdateResults = await Promise.all(cargoUpdatePromises);
          const successfulUpdates = cargoUpdateResults.filter(
            (r) => r.success
          ).length;
          const failedUpdates = cargoUpdateResults.length - successfulUpdates;

          if (failedUpdates > 0) {
            console.warn(
              `Failed to update ${failedUpdates} cargo records with invoice ID`
            );
          }

          // Additional success info for cargo updates
          if (failedUpdates === 0) {
            console.log(
              `All ${successfulUpdates} cargo records successfully linked to invoice`
            );
          }
        } catch (error) {
          console.error("Error updating cargo records with invoice ID:", error);
          toast.success(
            `Invoice created successfully, but some cargo records may not be linked`,
            {
              description: "Please check cargo records manually if needed.",
            }
          );
        }

        // Refresh data and clear selection
        fetchCargoData(true);
        updateState({ selectedCargos: new Set() });
      } else {
        toast.error(`Failed to create invoice: ${result.error}`);
      }
    } catch (error) {
      console.error("Error creating bulk invoice:", error);
      toast.error("Failed to create invoice");
    } finally {
      updateState({ bulkActionLoading: false });
    }
  }, [state.selectedCargos, state.cargoData, updateState, fetchCargoData]);

  // Bulk batch assignment handlers
  const handleBulkAssignToBatch = useCallback(
    async (batchId: string) => {
      const selectedIds = Array.from(state.selectedCargos);
      if (selectedIds.length === 0) {
        toast.error("Please select cargo items to assign");
        return;
      }

      try {
        updateState({ bulkActionLoading: true });

        // Assign each selected cargo to the batch
        const assignmentPromises = selectedIds.map((cargoId) =>
          cargoService.assignToBatch(cargoId, batchId)
        );

        const assignmentResults = await Promise.all(assignmentPromises);
        const successfulAssignments = assignmentResults.filter(
          (result) => result.success
        ).length;
        const failedAssignments =
          assignmentResults.length - successfulAssignments;

        if (successfulAssignments > 0) {
          toast.success(
            `Successfully assigned ${successfulAssignments} cargo(s) to batch`,
            {
              description:
                failedAssignments > 0
                  ? `${failedAssignments} assignment(s) failed`
                  : undefined,
            }
          );

          // Refresh data and clear selection
          fetchCargoData(true);
          updateState({ selectedCargos: new Set() });
        } else {
          toast.error("Failed to assign any cargo to batch");
        }
      } catch (error) {
        console.error("Error in bulk batch assignment:", error);
        toast.error("Failed to assign cargo to batch");
      } finally {
        updateState({ bulkActionLoading: false });
      }
    },
    [state.selectedCargos, updateState, fetchCargoData]
  );

  const handleBulkUnassignFromBatch = useCallback(async () => {
    const selectedIds = Array.from(state.selectedCargos);
    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to unassign");
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to unassign ${selectedIds.length} cargo item(s) from their batches?`
    );

    if (!confirmed) return;

    try {
      updateState({ bulkActionLoading: true });

      // Unassign each selected cargo from its batch
      const unassignmentPromises = selectedIds.map((cargoId) =>
        cargoService.removeFromBatch(cargoId)
      );

      const unassignmentResults = await Promise.all(unassignmentPromises);
      const successfulUnassignments = unassignmentResults.filter(
        (result) => result.success
      ).length;
      const failedUnassignments =
        unassignmentResults.length - successfulUnassignments;

      if (successfulUnassignments > 0) {
        toast.success(
          `Successfully unassigned ${successfulUnassignments} cargo(s) from batch`,
          {
            description:
              failedUnassignments > 0
                ? `${failedUnassignments} unassignment(s) failed`
                : undefined,
          }
        );

        // Refresh data and clear selection
        fetchCargoData(true);
        updateState({ selectedCargos: new Set() });
      } else {
        toast.error("Failed to unassign any cargo from batch");
      }
    } catch (error) {
      console.error("Error in bulk batch unassignment:", error);
      toast.error("Failed to unassign cargo from batch");
    } finally {
      updateState({ bulkActionLoading: false });
    }
  }, [state.selectedCargos, updateState, fetchCargoData]);

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    updateState({ selectedCargos: new Set() });
    toast.success("Selections cleared");
  }, [updateState]);

  // Export handlers
  const handleBulkExport = useCallback(
    (selectedIds?: string[]) => {
      const dataToExport =
        selectedIds && selectedIds.length > 0
          ? state.cargoData.filter((cargo) => selectedIds.includes(cargo.id))
          : state.cargoData;

      if (dataToExport.length === 0) {
        toast.error("No data to export");
        return;
      }

      updateState({
        exportDialog: {
          ...state.exportDialog,
          isOpen: true,
          data: dataToExport,
          config: {
            ...state.exportDialog.config,
            title:
              selectedIds && selectedIds.length > 0
                ? `Selected Cargo Export (${selectedIds.length} items)`
                : `All Cargo Export (${dataToExport.length} items)`,
          },
        },
      });
    },
    [state.cargoData, state.exportDialog, updateState]
  );

  const handleExportSelected = useCallback(() => {
    const selectedIds = Array.from(state.selectedCargos);
    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to export");
      return;
    }
    handleBulkExport(selectedIds);
  }, [state.selectedCargos, handleBulkExport]);

  const handleExportAll = useCallback(() => {
    handleBulkExport();
  }, [handleBulkExport]);

  const closeExportDialog = useCallback(() => {
    updateState({
      exportDialog: {
        ...state.exportDialog,
        isOpen: false,
        data: [],
      },
    });
  }, [state.exportDialog, updateState]);

  // Bulk task creation handlers
  const handleBulkCreateTasks = useCallback(() => {
    if (state.selectedCargos.size === 0) {
      toast.error("Please select cargo items to create tasks for");
      return;
    }
    updateState({ isBulkTaskDialogOpen: true });
  }, [state.selectedCargos.size, updateState]);

  const setIsBulkTaskDialogOpen = useCallback(
    (isOpen: boolean) => {
      updateState({ isBulkTaskDialogOpen: isOpen });
    },
    [updateState]
  );

  const selectedItemsForTasks = Array.from(state.selectedCargos).map(
    (cargoId) => {
      const cargo = state.cargoData.find((c) => c.id === cargoId);
      return {
        id: cargoId,
        name: cargo?.particular || cargo?.trackingNumber || "Unknown Cargo",
        identifier: cargo?.trackingNumber,
      };
    }
  );

  const handleTasksCreated = useCallback(() => {
    // Clear selections and refresh data
    updateState({ selectedCargos: new Set() });
    handleRefresh();
  }, [updateState, handleRefresh]);

  return {
    state,
    pagination,
    updateState,
    handleRefresh,
    handleCargoAction,
    handleDialogClose,
    handleCargoMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleBulkCreateInvoice,
    handleBulkAssignToBatch,
    handleBulkUnassignFromBatch,
    handleAssignCargoToBatch,
    handleUnassignCargoFromBatch,
    handleClearSelections,
    handleBulkExport,
    handleExportSelected,
    handleExportAll,
    closeExportDialog,
    handleTasksCreated,
    handleBulkCreateTasks,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    isBulkTaskDialogOpen: state.isBulkTaskDialogOpen,
  };
}
