"use client";

import { memo } from "react";
import {
  Package,
  Truck,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  Container,
  Weight,
  User2,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { type CargoStats } from "../utils/types";

interface CargoStatisticsProps {
  cargoStats: CargoStats;
  loading?: boolean;
}

/**
 * Statistics component for Cargo Management
 *
 * Displays key metrics and KPIs for cargo in a grid layout.
 * Memoized to prevent unnecessary re-renders when parent state changes.
 */
export const CargoStatistics = memo<CargoStatisticsProps>(
  ({ cargoStats, loading }) => {
    // Calculate completion rate
    const completionRate =
      cargoStats.totalCargos > 0
        ? (cargoStats.delivered / cargoStats.totalCargos) * 100
        : 0;

    return (
      <Listing.Statistics columns="grid-cols-4">
        <Listing.StatCard
          icon={Package}
          name="Total Cargo"
          value={cargoStats.totalCargos}
          valueType="number"
          caption={
            <span className="text-xs text-primary flex items-center gap-1">
              <TrendingUp className="h-3 w-3" /> All shipments
            </span>
          }
          color="primary"
          loading={loading}
        />

        <Listing.StatCard
          icon={Truck}
          name="In Transit"
          value={cargoStats.inTransit}
          valueType="number"
          caption={
            <span className="text-xs text-amber-600 flex items-center gap-1">
              <Truck className="h-3 w-3" /> Active shipments
            </span>
          }
          color="amber"
          loading={loading}
        />

        <Listing.StatCard
          icon={CheckCircle}
          name="Delivered"
          value={cargoStats.delivered}
          valueType="number"
          caption={
            <span className="text-xs text-green-600 flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {completionRate.toFixed(1)}% rate
            </span>
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={AlertTriangle}
          name="Issues"
          value={cargoStats.issues}
          valueType="number"
          caption={
            <span className="text-xs text-red-600 flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" /> Needs attention
            </span>
          }
          color="red"
          loading={loading}
        />

        <Listing.StatCard
          icon={DollarSign}
          name="Total Value"
          value={cargoStats.totalValue}
          valueType="dollar"
          caption={
            <span className="text-xs text-green-600 flex items-center gap-1">
              <DollarSign className="h-3 w-3" /> Aggregate value
            </span>
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={Container}
          name="Total CBM"
          value={cargoStats.totalCBM}
          valueType="number"
          caption={
            <span className="text-xs text-purple-600 flex items-center gap-1">
              <Container className="h-3 w-3" /> Overall volume
            </span>
          }
          color="purple"
          loading={loading}
        />

        <Listing.StatCard
          icon={Container}
          name="Total CTN"
          value={cargoStats.totalCTN}
          valueType="number"
          caption={
            <span className="text-xs text-primary flex items-center gap-1">
              <Container className="h-3 w-3" /> Overall cartons
            </span>
          }
          color="primary"
          loading={loading}
        />
        <Listing.StatCard
          icon={Weight}
          name="Total Weight"
          value={cargoStats.totalWeight}
          valueType="number"
          caption={
            <span className="text-xs text-blue-600 flex items-center gap-1">
              <Weight className="h-3 w-3" /> Overall weight
            </span>
          }
          color="blue"
          loading={loading}
        />

        <Listing.StatCard
          icon={User2}
          name="Total Customers"
          value={cargoStats.totalCustomers}
          valueType="number"
          caption={
            <span className="text-xs text-indigo-600 flex items-center gap-1">
              <User2 className="h-3 w-3" /> Unique customers
            </span>
          }
          color="purple"
          loading={loading}
        />
      </Listing.Statistics>
    );
  }
);

CargoStatistics.displayName = "CargoStatistics";
