"use client";

import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { useCargoManagement } from "../hooks/useCargoManagement";

// Components
import { CargoManagementHeader } from "./CargoManagementHeader";
import { CargoStatistics } from "./CargoStatistics";
import { CargoContent } from "./index";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";
import { NewCargoDialog, QRCodeDialog, CargoDetailsDialog } from "./Dialog";

/**
 * Main container component for Cargo Management
 *
 * This component manages all the state and business logic for the cargo management page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function CargoManagementContainer() {
  const { shouldShowCreateButton } = useRBAC();

  // Use custom hook for all cargo management logic
  const {
    state,
    updateState,
    pagination,
    handleRefresh,
    handleCargoAction,
    handleCargoMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleBulkCreateInvoice,
    handleDialogClose,
    handleClearSelections,
    handleExportSelected,
    handleExportAll,
    closeExportDialog,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  } = useCargoManagement();

  // Wrapper function to handle the type mismatch
  const handleShouldShowCreateButton = (entity: string) => {
    return shouldShowCreateButton(entity as any);
  };

  return (
    <div className="p-6 space-y-8">
      <CargoManagementHeader
        loading={state.loading}
        shouldShowCreateButton={handleShouldShowCreateButton}
        onRefresh={handleRefresh}
        onCreateCargo={() => handleCargoAction("create")}
      />

      <div className="space-y-8">
        <CargoStatistics cargoStats={state.cargoStats} />

        <CargoContent
          state={state}
          onStateUpdate={updateState}
          onRefresh={handleRefresh}
          onCargoAction={handleCargoAction}
          onCargoMutation={handleCargoMutation}
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onBulkDelete={handleBulkDelete}
          onBulkCreateInvoice={handleBulkCreateInvoice}
          onBulkCreateTasks={handleBulkCreateTasks}
          onClearSelections={handleClearSelections}
          onExportSelected={handleExportSelected}
          onExportAll={handleExportAll}
          onCloseExportDialog={closeExportDialog}
          pagination={pagination}
        />
      </div>

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable="cargos"
        title="Create Tasks for Selected Cargo"
      />

      {/* New Cargo Dialog */}
      <NewCargoDialog
        open={state.isNewCargoOpen}
        onOpenChange={(open) => {
          if (!open) {
            handleDialogClose("create");
          }
        }}
        onSuccess={handleCargoMutation}
      />

      {/* QR Code Dialog */}
      <QRCodeDialog
        open={state.qrCodeDialog.open}
        cargo={state.qrCodeDialog.cargo}
        onOpenChange={(open) => {
          if (!open) {
            handleDialogClose("qr-code");
          }
        }}
      />

      {/* Cargo Details Dialog */}
      <CargoDetailsDialog
        open={state.cargoDetailsDialog.open}
        cargo={state.cargoDetailsDialog.cargo}
        onOpenChange={(open) => {
          if (!open) {
            handleDialogClose("cargo-details");
          }
        }}
        onCargoUpdate={handleCargoMutation}
        onQRCodeAction={(cargo) => {
          handleCargoAction("qr-code", cargo);
        }}
      />
    </div>
  );
}
