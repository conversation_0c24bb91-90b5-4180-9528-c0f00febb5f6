"use client";

import { memo } from "react";
import { Listing } from "@/modules/listing";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Trash2, FileText, Plus, Download, CheckSquare } from "lucide-react";
import { ExportDialog } from "@/components/ui/export-dialog";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import {
  BulkStatusSelector,
  CARGO_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { type CargoManagementState, type CargoDisplay } from "../utils/types";
import {
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";
import {
  CargoTableColumns,
  CargoCardRenderer,
  NewCargoDialog,
  QRCodeDialog,
} from "./index";

interface CargoContentProps {
  state: CargoManagementState;
  onStateUpdate: (updates: Partial<CargoManagementState>) => void;
  onRefresh: () => void;
  onCargoAction: (action: string, cargo?: CargoDisplay) => void;
  onCargoMutation: () => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateInvoice: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections: () => void;
  onExportSelected: () => void;
  onExportAll: () => void;
  onCloseExportDialog: () => void;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    nextPage: () => void;
    previousPage: () => void;
    firstPage: () => void;
    lastPage: () => void;
    goToPage: (page: number) => void;
    setItemsPerPage: (itemsPerPage: number) => void;
  };
}

/**
 * Content component for Cargo Management
 *
 * Handles the main listing, filtering, and display of cargo data.
 * Uses the Listing layout component for consistent UI patterns.
 */
export const CargoContent = memo<CargoContentProps>(
  ({
    state,
    onStateUpdate,
    onRefresh,
    onCargoAction,
    onCargoMutation,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateInvoice,
    onBulkCreateTasks,
    onClearSelections,
    onExportSelected,
    onExportAll,
    onCloseExportDialog,
    pagination,
  }) => {
    // Render bulk actions
    const renderBulkActions = () => (
      <>
        <BulkStatusSelector
          statusOptions={CARGO_STATUS_OPTIONS}
          onStatusUpdate={onBulkStatusUpdate}
          disabled={state.bulkActionLoading}
          placeholder="Mark as..."
          onAfterUpdate={onRefresh}
        />
        <Button
          variant="outline"
          size="sm"
          onClick={onExportSelected}
          className="gap-2"
          disabled={state.selectedCargos.size === 0}
        >
          <Download size={16} />
          Export Selected
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onBulkCreateInvoice}
          className="gap-2"
          disabled={state.bulkActionLoading}
        >
          <FileText size={16} />
          Create Invoice
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onBulkCreateTasks}
          className="gap-2"
          disabled={state.bulkActionLoading || state.selectedCargos.size === 0}
        >
          <CheckSquare size={16} />
          Create Tasks
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={onBulkDelete}
          className="gap-2"
        >
          <Trash2 size={16} />
          Delete
        </Button>
      </>
    );

    // Define table columns for dynamic filtering
    const cargoTableColumns: TableColumn[] = [
      {
        key: "trackingNumber",
        label: "Tracking Number",
        type: "string",
        searchable: true,
      },
      {
        key: "chinaTrackingNumber",
        label: "China Tracking Number",
        type: "string",
        searchable: true,
      },
      {
        key: "batchCode",
        label: "Batch Code",
        type: "string",
        searchable: true,
      },
      { key: "type", label: "Type", type: "enum", searchable: true },
      { key: "origin", label: "Origin", type: "string", searchable: true },
      {
        key: "destination",
        label: "Destination",
        type: "string",
        searchable: true,
      },
      { key: "status", label: "Status", type: "enum", searchable: true },
      { key: "ctn", label: "CTN", type: "number", searchable: true },
      { key: "cbm", label: "CBM", type: "number", searchable: true },
      { key: "weight", label: "Weight", type: "string", searchable: true },
      { key: "customer", label: "Customer", type: "string", searchable: true },
      {
        key: "customerPhone",
        label: "Customer Phone",
        type: "string",
        searchable: true,
      },
      { key: "date", label: "Date", type: "date", searchable: true },
      {
        key: "particular",
        label: "Description",
        type: "string",
        searchable: true,
      },
    ];

    // Use server-side filtered data directly (no client-side filtering needed)
    // The search and filtering is now handled by the cargoService.searchCargos() method
    const filteredData = state.cargoData;

    // Categories for filtering
    const categories = [
      { key: "in-transit", label: "In Transit" },
      { key: "delivered", label: "Delivered" },
      { key: "processing", label: "Processing" },
      { key: "issues", label: "Issues" },
    ];

    return (
      <Listing className="space-y-6">
        <Listing.Filters
          searchTerm={state.searchTerm}
          onSearchChange={(term: string) =>
            onStateUpdate({ searchTerm: term, currentPage: 1 })
          }
          onRefresh={onRefresh}
          loading={state.loading}
          columnFilters={state.columnFilters}
          onColumnFilterAdd={(filter: ColumnFilter) =>
            onStateUpdate({
              columnFilters: [...state.columnFilters, filter],
              currentPage: 1,
            })
          }
          onColumnFilterRemove={(index: number) =>
            onStateUpdate({
              columnFilters: state.columnFilters.filter((_, i) => i !== index),
              currentPage: 1,
            })
          }
          enableDynamicFilters={true}
          columns={cargoTableColumns}
          tableData={state.cargoData}
          defaultFilterColumn="trackingNumber"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={state.selectedCargos.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="cargos"
          length={filteredData.length}
          viewMode={state.viewMode}
          onViewModeChange={(mode: "cards" | "table") =>
            onStateUpdate({ viewMode: mode })
          }
          categoryFilter={state.categoryFilter}
          onCategoryFilterChange={(filter: string) =>
            onStateUpdate({ categoryFilter: filter, currentPage: 1 })
          }
          categories={categories}
          actions={
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={onExportAll} className="gap-2">
                <Download size={16} />
                Export All
              </Button>
              <ProtectedCreateButton entity="cargo">
                <Button onClick={() => onStateUpdate({ isNewCargoOpen: true })}>
                  <Plus size={16} />
                  New Cargo
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {state.viewMode === "cards" ? (
          <Listing.Cards
            data={filteredData}
            loading={state.loading}
            renderCard={(cargo: CargoDisplay) => (
              <CargoCardRenderer cargo={cargo} onAction={onCargoAction} />
            )}
            emptyState={
              <div className="text-center py-12">
                <p className="text-gray-500">No cargo found</p>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={filteredData}
            columns={CargoTableColumns({
              onAction: onCargoAction,
            })}
            loading={state.loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(state.selectedCargos)}
            onSelectionChange={(selectedIds) =>
              onStateUpdate({ selectedCargos: new Set(selectedIds) })
            }
            getRowId={(item) => item.id}
            pagination={pagination}
            emptyState={
              <div className="text-center py-12">
                <p className="text-gray-500">No cargo found</p>
              </div>
            }
          />
        )}

        {/* Dialogs */}
        <NewCargoDialog
          open={state.isNewCargoOpen}
          onOpenChange={(open) => onStateUpdate({ isNewCargoOpen: open })}
          onSuccess={onCargoMutation}
        />

        <QRCodeDialog
          open={state.qrCodeDialog.open}
          cargo={state.qrCodeDialog.cargo}
          onOpenChange={(open) =>
            onStateUpdate({
              qrCodeDialog: {
                open,
                cargo: open ? state.qrCodeDialog.cargo : null,
              },
            })
          }
        />

        {/* Export Dialog */}
        <ExportDialog
          isOpen={state.exportDialog.isOpen}
          onClose={onCloseExportDialog}
          data={state.exportDialog.data}
          title={state.exportDialog.config.title}
          filename={state.exportDialog.config.filename}
          excludeColumns={state.exportDialog.config.excludeColumns}
          columnMapping={state.exportDialog.config.columnMapping}
          onSuccess={(type, filename) => {
            console.log(`Export successful: ${type} - ${filename}`);
          }}
          onError={(error) => {
            console.error("Export error:", error);
          }}
        />
      </Listing>
    );
  }
);

CargoContent.displayName = "CargoContent";
