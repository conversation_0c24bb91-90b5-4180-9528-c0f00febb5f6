"use client";

import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { useBatchManagement } from "../hooks/useBatchManagement";
import { type BatchWithRelations, batchService } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { showToast } from "@/lib/utils";

// Components
import { Overview } from "@/modules/layouts/overview";
import { BatchManagementHeader } from "./BatchManagementHeader";
import { BatchStatistics } from "./BatchStatistics";
import { BatchList } from "./BatchList";
import { BatchDialogs } from "./BatchDialogs";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";

// Types
export interface BatchStats {
  totalBatches: number;
  activeBatches: number;
  completedBatches: number;
  totalCapacity: number;
  utilizationRate: number;
}

// Types for display data
export interface BatchDisplay {
  id: string;
  code: string;
  name: string;
  type: string;
  weight: string;
  volume: string;
  assignedCargo: number;
  status: string;
  date: string;
}

export interface BatchManagementState {
  loading: boolean;
  refreshing: boolean;
  viewMode: "cards" | "table";
  searchTerm: string;
  categoryFilter: string;
  currentPage: number;
  columnFilters: ColumnFilter[];
  batchStats: BatchStats;
  batchData: BatchDisplay[];
  batches: BatchWithRelations[];

  // Dialog states
  isNewBatchOpen: boolean;
  isEditBatchDialogOpen: boolean;
  selectedBatchForEdit: BatchWithRelations | null;
  isDeleteBatchDialogOpen: boolean;
  selectedBatchForDelete: BatchWithRelations | null;

  // Checkbox state
  selectedItems: Set<string>;

  // Bulk task creation state
  isBulkTaskDialogOpen: boolean;
}

/**
 * Main container component for Batch Management
 *
 * This component manages all the state and business logic for the batch management page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function BatchManagementContainer() {
  const { shouldShowCreateButton } = useRBAC();

  // Use custom hook for all batch management logic
  const {
    state,
    filteredBatches,
    handleRefresh,
    handleBatchAction,
    handleDialogClose,
    handleBatchMutation,
    handleBulkStatusUpdate,
    handleDeleteBatch,
    handleBulkDelete,
    handleClearSelections,
    updateState,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  } = useBatchManagement();

  return (
    <Overview className="p-6 space-y-8">
      <BatchManagementHeader
        loading={state.loading}
        shouldShowCreateButton={shouldShowCreateButton}
        onRefresh={handleRefresh}
        onCreateBatch={() => handleBatchAction("create")}
      />
      <div className="flex flex-col space-y-8">
        <BatchStatistics
          batchStats={state.batchStats}
          loading={state.loading}
        />
        <BatchList
          data={filteredBatches}
          loading={state.loading}
          viewMode={state.viewMode}
          searchTerm={state.searchTerm}
          categoryFilter={state.categoryFilter}
          currentPage={state.currentPage}
          itemsPerPage={10}
          listType="batches"
          onViewModeChange={(mode: "cards" | "table") =>
            updateState({ viewMode: mode })
          }
          onSearchChange={(term: string) => updateState({ searchTerm: term })}
          onCategoryFilterChange={(filter: string) =>
            updateState({ categoryFilter: filter })
          }
          onPageChange={(page: number) => updateState({ currentPage: page })}
          onRefresh={handleRefresh}
          onItemAction={handleBatchAction}
          selectedItems={state.selectedItems}
          setSelectedItems={(items: Set<string>) =>
            updateState({ selectedItems: items })
          }
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onBulkDelete={handleBulkDelete}
          onBulkCreateTasks={handleBulkCreateTasks}
          onClearSelections={handleClearSelections}
        />
      </div>

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable="batches"
        title="Create Tasks for Selected Batches"
      />

      <BatchDialogs
        state={state}
        onClose={handleDialogClose}
        onBatchMutation={handleBatchMutation}
        onDeleteBatch={handleDeleteBatch}
      />
    </Overview>
  );
}
