@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 98%;
        --background-secondary: 0 0% 96%;
        --background-tertiary: 0 0% 94%;
        --background-solid: 220 29% 5%;

        --foreground: 220 18% 13%;
        --foreground-secondary: 222 10% 29%;
        --foreground-tertiary: 218 7% 36%;
        --foreground-quaternary: 219 5% 47%;

        --brand: 11 98% 46%;
        --brand-foreground: 0 0% 100%;
        --brand-hover: 11 98% 42%;

        --card: var(--background);
        --card-foreground: var(--foreground);
        --popover: var(--background);
        --popover-foreground: var(--foreground);
        --primary: var(--brand);
        --primary-foreground: var(--brand-foreground);
        --muted: var(--background-tertiary);
        --muted-foreground: var(--foreground-tertiary);
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 219 5% 88%;
        --input: 219 5% 92%;
        --ring: var(--brand);
        --radius: 0.5rem;
    }

    * {
        @apply border-border;
    }

    body {
        @apply bg-background-secondary text-foreground font-sans;
        font-feature-settings: "rlig" 1, "calt" 1;
    }

}

@layer components {
    .input-base {
        @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-foreground placeholder:text-foreground-quaternary ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    }

    .button-base {
        @apply inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
    }

    .button-primary {
        @apply bg-primary text-primary-foreground hover:bg-brand-hover;
    }

    .button-outline {
        @apply border border-input bg-transparent hover:bg-background-tertiary hover:text-foreground;
    }

    .button-sm {
        @apply h-9 px-3;
    }

    .button-md {
        @apply h-10 px-4 py-2;
    }

    .button-ghost {
        @apply hover:bg-background-tertiary hover:text-foreground-secondary;
    }

    .switch-base {
        @apply inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input;
    }

    .switch-thumb {
        @apply pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0;
    }

    .select-base {
        @apply h-10 px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent w-full appearance-none text-foreground;
    }

    .select-icon {
        @apply absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none;
    }

    .select-icon svg {
        @apply h-4 w-4 text-foreground-quaternary;
    }

    .stat-card {
        @apply bg-background rounded-lg shadow p-4 md:p-5;
    }

    .chart-card {
        @apply bg-background rounded-lg shadow p-4 md:p-6;
    }

    .table-card {
        @apply bg-background rounded-lg shadow overflow-hidden;
    }

    .table-header {
        @apply px-4 py-3 text-left text-xs font-medium text-foreground-quaternary uppercase tracking-wider bg-background-tertiary;
    }

    .table-cell {
        @apply px-4 py-3 whitespace-nowrap text-sm text-foreground-secondary;
    }

    .status-badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize;
    }

    .cargo-table-row {
        @apply hover:bg-background-tertiary cursor-pointer;
    }

    .notification-item {
        @apply flex items-start gap-3 px-4 py-3 border-b border-border last:border-b-0;
    }

    .notification-dot {
        @apply w-2 h-2 rounded-full flex-shrink-0 mt-1.5;
    }

    .notification-tab-trigger {
        @apply justify-start w-full data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=inactive]:hover:bg-background-tertiary data-[state=inactive]:hover:text-foreground data-[state=inactive]:text-foreground-secondary px-4 py-2.5 text-sm font-medium rounded-lg;
    }
}

@layer utilities {
    .text-brand-primary {
        color: hsl(var(--brand));
    }

    .hover\:text-brand-primary-hover:hover {
        color: hsl(var(--brand-hover));
    }

    .bg-brand-primary {
        background-color: hsl(var(--brand));
    }

    .hover\:bg-brand-primary-hover:hover {
        background-color: hsl(var(--brand-hover));
    }

    .ring-brand-primary {
        --tw-ring-color: hsl(var(--brand));
    }

    .focus\:ring-brand-primary:focus {
        --tw-ring-color: hsl(var(--brand));
    }
}

/* Overrides */
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}