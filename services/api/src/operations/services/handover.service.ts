import {
  Injectable,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import * as jsQR from 'jsqr';
import { Cargo } from '../entities/cargo.entity';
import { Account } from '../../system/entities/account.entity';
import { User } from '../../system/entities/user.entity';
import { Handover } from '../entities/handover.entity';
import { LogsService } from '../../system/services/logs.service';
import { NotificationService } from '../../system/services/notification.service';
import { DocumentService } from '../../system/services/document.service';
import { Status } from '../../common/enums/status.enum';
import { WebsocketGateway } from '../../websocket/websocket.gateway';

interface QRCodeData {
  id: string;
  type: string;
}

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: Date;
}

interface BiometricVerificationResult {
  isValid: boolean;
  verifiedAt: Date;
  type: string;
}

@Injectable()
export class HandoverService extends BaseService {
  private readonly VALID_HANDOVER_STATUSES = [
    Status.DELIVERED,
    Status.PICKED_UP,
    Status.RELEASED,
  ];
  private readonly BIOMETRIC_VERIFICATION_WINDOW = 5 * 60; // 5 minutes in seconds

  constructor(
    @InjectRepository(Cargo)
    private cargoRepository: Repository<Cargo>,
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Handover)
    private handoverRepository: Repository<Handover>,
    private logsService: LogsService,
    private notificationService: NotificationService,
    private documentService: DocumentService,
    private websocketGateway: WebsocketGateway,
  ) {}

  // QR Code Related Methods
  private async parseAndValidateQRData(qrData: string): Promise<QRCodeData> {
    try {
      const parsedData = JSON.parse(qrData);

      if (!parsedData.id || !parsedData.type || parsedData.type !== 'cargo') {
        throw new BadRequestException('Invalid QR code format');
      }

      return parsedData;
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(
        'Invalid QR code format: Unable to parse data',
      );
    }
  }

  private async validateCargoForHandover(cargoId: string): Promise<Cargo> {
    const cargo = await this.cargoRepository.findOne({
      where: { id: cargoId },
      relations: ['customer', 'batch'],
    });

    if (!cargo) {
      throw new NotFoundException(`Cargo with ID ${cargoId} not found`);
    }

    if (!this.VALID_HANDOVER_STATUSES.includes(cargo.status as Status)) {
      throw new BadRequestException(
        `Cargo status (${cargo.status}) is not valid for handover`,
      );
    }

    return cargo;
  }

  async verifyQrCode(qrData: string, account: Account): Promise<any> {
    try {
      const parsedData = await this.parseAndValidateQRData(qrData);
      const cargo = await this.validateCargoForHandover(parsedData.id);

      await this.logVerificationAttempt(cargo, account);

      return {
        verified: true,
        cargo: this.formatCargoResponse(cargo),
      };
    } catch (error) {
      this.handleError(error, 'Failed to verify QR code');
    }
  }

  async scanQrCodeImage(file: Buffer, account: Account): Promise<any> {
    try {
      // Without canvas, we can't process image data directly
      // Instead, we'll inform the client to use the text-based QR code verification
      throw new BadRequestException(
        'Image-based QR code scanning is not available. ' +
          'Please use the text-based QR code verification by sending the QR code data directly.',
      );
    } catch (error) {
      this.handleError(error, 'Failed to process QR code image');
    }
  }

  // Handover Process Methods
  async completeHandover(
    cargoId: string,
    receiverId: string,
    biometricData: string,
    handoverNotes: string,
    account: Account,
  ): Promise<any> {
    try {
      const [cargo, receiver] = await Promise.all([
        this.validateCargoForHandover(cargoId),
        this.validateReceiver(receiverId),
      ]);

      const biometricResult = await this.verifyBiometricData(
        biometricData,
        receiver,
      );
      if (!biometricResult.isValid) {
        throw new BadRequestException('Biometric verification failed');
      }

      const handover = await this.createHandoverRecord({
        cargo,
        sender: account,
        receiver,
        notes: handoverNotes,
        biometricResult,
      });

      await this.handlePostHandoverTasks(handover, cargo, account);

      return this.formatHandoverResponse(handover);
    } catch (error) {
      this.handleError(error, 'Failed to complete handover');
    }
  }

  private async validateReceiver(receiverId: string): Promise<Account> {
    const receiver = await this.accountRepository.findOne({
      where: { id: receiverId },
      relations: ['user'],
    });

    if (!receiver) {
      throw new NotFoundException(
        `Receiver account with ID ${receiverId} not found`,
      );
    }

    return receiver;
  }

  private async verifyBiometricData(
    biometricData: string,
    account: Account,
  ): Promise<BiometricVerificationResult> {
    try {
      if (!biometricData || biometricData.length < 10) {
        return { isValid: false, verifiedAt: new Date(), type: 'fingerprint' };
      }

      const [encodedId, timestamp, signature] = biometricData.split('.');

      if (!encodedId || !timestamp || !signature) {
        return { isValid: false, verifiedAt: new Date(), type: 'fingerprint' };
      }

      const decodedId = atob(encodedId);
      if (!decodedId.includes(account.id)) {
        return { isValid: false, verifiedAt: new Date(), type: 'fingerprint' };
      }

      const bioTimestamp = parseInt(timestamp, 10);
      const currentTime = Math.floor(Date.now() / 1000);
      if (currentTime - bioTimestamp > this.BIOMETRIC_VERIFICATION_WINDOW) {
        return { isValid: false, verifiedAt: new Date(), type: 'fingerprint' };
      }

      return { isValid: true, verifiedAt: new Date(), type: 'fingerprint' };
    } catch (error) {
      return { isValid: false, verifiedAt: new Date(), type: 'fingerprint' };
    }
  }

  private async createHandoverRecord({
    cargo,
    sender,
    receiver,
    notes,
    biometricResult,
  }: {
    cargo: Cargo;
    sender: Account;
    receiver: Account;
    notes: string;
    biometricResult: BiometricVerificationResult;
  }): Promise<Handover> {
    const handover = new Handover();
    handover.handoverDate = new Date();
    handover.handoverNotes = notes;
    handover.qrVerification = true;
    handover.biometricVerification = biometricResult.isValid;
    handover.verificationData = {
      qrVerifiedAt: new Date(),
      biometricVerifiedAt: biometricResult.verifiedAt,
      biometricType: biometricResult.type,
    };
    handover.status = Status.COMPLETED;
    handover.sender = sender;
    handover.receiver = receiver;
    handover.cargo = cargo;
    handover.locationData = await this.getLocationData();

    return this.handoverRepository.save(handover);
  }

  private async getLocationData(): Promise<LocationData> {
    // In a real implementation, this would get actual GPS coordinates
    return {
      latitude: 0,
      longitude: 0,
      accuracy: 0,
      timestamp: new Date(),
    };
  }

  private async handlePostHandoverTasks(
    handover: Handover,
    cargo: Cargo,
    account: Account,
  ): Promise<void> {
    await Promise.all([
      this.updateCargoStatus(cargo),
      this.createHandoverLog(handover, account),
      this.createHandoverDocument(handover, account),
      this.sendHandoverNotifications(handover, account),
      this.emitHandoverWebsocketEvent(handover),
    ]);
  }

  private async updateCargoStatus(cargo: Cargo): Promise<void> {
    if (cargo.status !== Status.PICKED_UP) {
      cargo.status = Status.PICKED_UP;
      await this.cargoRepository.save(cargo);
    }
  }

  private async createHandoverLog(
    handover: Handover,
    account: Account,
  ): Promise<void> {
    await this.logsService.create(
      {
        event: 'Cargo Handover Completed',
        message: `Cargo ${handover.cargo.trackingNumber} handed over from ${handover.sender.user.name} to ${handover.receiver.user.name}`,
        associatedTable: 'handovers',
        associatedId: handover.id,
      },
      account,
    );
  }

  private async createHandoverDocument(
    handover: Handover,
    account: Account,
  ): Promise<void> {
    await this.documentService.create(
      {
        name: `Handover Receipt for ${handover.cargo.trackingNumber}`,
        path: `/handovers/receipt-${handover.cargo.id}-${new Date().getTime()}.pdf`,
        category: 'handover',
        description: 'Cargo handover receipt',
        associatedTable: 'handovers',
        associatedId: handover.id,
        details: {
          cargoId: handover.cargo.id,
          trackingNumber: handover.cargo.trackingNumber,
          senderId: handover.sender.id,
          senderName: handover.sender.user.name,
          receiverId: handover.receiver.id,
          receiverName: handover.receiver.user.name,
          handoverDate: handover.handoverDate,
          biometricVerified: handover.biometricVerification,
          qrVerified: handover.qrVerification,
        },
      },
      account,
    );
  }

  private async sendHandoverNotifications(
    handover: Handover,
    account: Account,
  ): Promise<void> {
    await this.notificationService.create(
      {
        name: 'Cargo Handover Completed',
        message: `Your cargo ${handover.cargo.trackingNumber} has been received by ${handover.receiver.user.name}`,
        associatedTable: 'handovers',
        associatedId: handover.id,
      },
      account,
    );
  }

  private async emitHandoverWebsocketEvent(handover: Handover): Promise<void> {
    this.websocketGateway.emitToCargo(
      handover.cargo.id,
      'cargo_handover_completed',
      {
        id: handover.id,
        cargoId: handover.cargo.id,
        trackingNumber: handover.cargo.trackingNumber,
        receiverId: handover.receiver.id,
        receiverName: handover.receiver.user.name,
        handoverDate: handover.handoverDate,
      },
    );
  }

  // Query Methods
  async getHandoverHistory(
    cargoId?: string,
    options?: { skip: number; take: number },
  ): Promise<{ handovers: Handover[]; total: number }> {
    const queryBuilder = this.handoverRepository
      .createQueryBuilder('handover')
      .leftJoinAndSelect('handover.sender', 'sender')
      .leftJoinAndSelect('handover.receiver', 'receiver')
      .leftJoinAndSelect('handover.cargo', 'cargo')
      .leftJoinAndSelect('sender.user', 'senderUser')
      .leftJoinAndSelect('receiver.user', 'receiverUser')
      .where('handover.status != :inactiveStatus', {
        inactiveStatus: 'INACTIVE',
      })
      .orderBy('handover.handoverDate', 'DESC');

    if (cargoId) {
      queryBuilder.andWhere('cargo.id = :cargoId', { cargoId });
    }

    if (options) {
      queryBuilder.skip(options.skip).take(options.take);
    }

    const [handovers, total] = await queryBuilder.getManyAndCount();
    return { handovers, total };
  }

  async getHandoverById(id: string): Promise<Handover> {
    const handover = await this.handoverRepository.findOne({
      where: {
        id,
        status: Not('INACTIVE'),
      },
      relations: [
        'sender',
        'receiver',
        'cargo',
        'sender.user',
        'receiver.user',
        'cargo.customer',
      ],
    });

    if (!handover) {
      throw new NotFoundException(`Handover with ID ${id} not found`);
    }

    return handover;
  }

  // Helper Methods
  private async logVerificationAttempt(
    cargo: Cargo,
    account: Account,
  ): Promise<void> {
    await this.logsService.create(
      {
        event: 'QR Code Verification',
        message: `QR code for cargo ${cargo.trackingNumber} verified by ${account.user.name}`,
        associatedTable: 'cargos',
        associatedId: cargo.id,
      },
      account,
    );
  }

  private formatCargoResponse(cargo: Cargo): any {
    return {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
      particular: cargo.particular,
      status: cargo.status,
      customer: {
        id: cargo.customer.id,
        name: cargo.customer.name,
        email: cargo.customer.email,
        phone: cargo.customer.phone,
      },
      batch: cargo.batch
        ? {
            id: cargo.batch.id,
            code: cargo.batch.code,
          }
        : null,
    };
  }

  private formatHandoverResponse(handover: Handover): any {
    return {
      success: true,
      handoverId: handover.id,
      cargo: {
        id: handover.cargo.id,
        trackingNumber: handover.cargo.trackingNumber,
        status: handover.cargo.status,
      },
      receiver: {
        id: handover.receiver.id,
        name: handover.receiver.user.name,
      },
      handoverDate: handover.handoverDate,
    };
  }

  private handleError(error: any, defaultMessage: string): never {
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }
    throw new InternalServerErrorException(
      defaultMessage + (error.message ? `: ${error.message}` : ''),
    );
  }
}
