import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { Cargo } from '../entities/cargo.entity';
import { Customer } from '../entities/customer.entity';
import { Batch } from '../entities/batch.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateCargoDto } from '../dto/create-cargo.dto';
import { UpdateCargoDto } from '../dto/update-cargo.dto';
import { LogsService } from '../../system/services/logs.service';
import { NotificationService } from '../../system/services/notification.service';
import { WebsocketGateway } from '../../websocket/websocket.gateway';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class CargoService {
  constructor(
    @InjectRepository(Cargo)
    private cargoRepository: Repository<Cargo>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Batch)
    private batchRepository: Repository<Batch>,
    private logsService: LogsService,
    private notificationService: NotificationService,
    private websocketGateway: WebsocketGateway,
  ) {}

  private async validateAndGetCustomer(customerId: string): Promise<Customer> {
    const customer = await this.customerRepository.findOne({
      where: { id: customerId },
    });

    if (!customer) {
      throw new NotFoundException(`Customer with ID ${customerId} not found`);
    }

    return customer;
  }

  private async validateAndGetBatch(batchId?: string): Promise<Batch | null> {
    if (!batchId) return null;

    const batch = await this.batchRepository.findOne({
      where: { id: batchId },
    });

    if (!batch) {
      throw new NotFoundException(`Batch with ID ${batchId} not found`);
    }

    return batch;
  }

  private async createAndSaveCargo(
    createCargoDto: CreateCargoDto,
    customer: Customer,
    batch: Batch | null,
    account: Account,
    trackingNumber: string,
  ): Promise<Cargo> {
    const cargo = this.cargoRepository.create({
      ...createCargoDto,
      trackingNumber,
      customer,
      batch: batch || undefined,
      account,
    });

    const savedCargo = await this.cargoRepository.save(cargo);
    return savedCargo;
  }

  private async handlePostCreationTasks(
    savedCargo: Cargo,
    customer: Customer,
    trackingNumber: string,
    account: Account,
  ): Promise<void> {
    // Create log entry
    await this.logsService.create(
      {
        event: 'Cargo Created',
        message: `Cargo ${trackingNumber} created for customer ${customer.name}`,
        associatedTable: 'cargos',
        associatedId: savedCargo.id,
      },
      account,
    );

    // Create notification
    await this.notificationService.create(
      {
        name: 'Cargo Registered',
        message: `Your cargo ${trackingNumber} has been registered successfully`,
        associatedTable: 'cargos',
        associatedId: savedCargo.id,
      },
      account,
    );

    // Emit websocket event
    this.websocketGateway.emitToUser(customer.id, 'cargo_created', {
      id: savedCargo.id,
      trackingNumber: savedCargo.trackingNumber,
      status: savedCargo.status,
    });
  }

  async create(
    createCargoDto: CreateCargoDto,
    account: Account,
  ): Promise<Cargo> {
    try {
      // Validate and get related entities
      const customer = await this.validateAndGetCustomer(
        createCargoDto.customerId,
      );
      const batch = await this.validateAndGetBatch(createCargoDto.batchId);

      // Generate tracking number
      const trackingNumber = this.generateTrackingNumber(createCargoDto);

      // Create and save cargo
      const savedCargo = await this.createAndSaveCargo(
        createCargoDto,
        customer,
        batch,
        account,
        trackingNumber,
      );

      // Handle post-creation tasks (logs, notifications, websocket)
      await this.handlePostCreationTasks(
        savedCargo,
        customer,
        trackingNumber,
        account,
      );

      return savedCargo;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create cargo');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    status?: string;
    batchId?: string;
    customerId?: string;
  }): Promise<{ cargos: Cargo[]; total: number }> {
    const { skip, take, status, batchId, customerId } = options;

    const queryBuilder = this.cargoRepository
      .createQueryBuilder('cargo')
      .leftJoinAndSelect('cargo.customer', 'customer')
      .leftJoinAndSelect('cargo.batch', 'batch')
      .leftJoinAndSelect('cargo.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .where('cargo.status != :inactiveStatus', { inactiveStatus: 'INACTIVE' });

    if (status) {
      queryBuilder.andWhere('cargo.status = :status', { status });
    }

    if (batchId) {
      queryBuilder.andWhere('cargo.batch.id = :batchId', { batchId });
    }

    if (customerId) {
      queryBuilder.andWhere('cargo.customer.id = :customerId', { customerId });
    }

    queryBuilder.skip(skip).take(take);

    const [cargos, total] = await queryBuilder.getManyAndCount();

    return { cargos, total };
  }

  async findOne(id: string): Promise<Cargo> {
    const cargo = await this.cargoRepository.findOne({
      where: {
        id,
        status: Not('INACTIVE'),
      },
      relations: ['customer', 'batch', 'account', 'account.user'],
    });

    if (!cargo) {
      throw new NotFoundException(`Cargo with ID ${id} not found`);
    }

    return cargo;
  }

  async update(
    id: string,
    updateCargoDto: UpdateCargoDto,
    account: Account,
  ): Promise<Cargo> {
    const cargo = await this.findOne(id);

    try {
      let customer = cargo.customer;
      if (
        updateCargoDto.customerId &&
        updateCargoDto.customerId !== cargo.customer.id
      ) {
        const foundCustomer = await this.customerRepository.findOne({
          where: { id: updateCargoDto.customerId },
        });

        if (!foundCustomer) {
          throw new NotFoundException(
            `Customer with ID ${updateCargoDto.customerId} not found`,
          );
        }
      }

      let batch: Batch | null = cargo.batch;
      if (
        updateCargoDto.batchId &&
        (!cargo.batch || updateCargoDto.batchId !== cargo.batch.id)
      ) {
        batch = await this.batchRepository.findOne({
          where: { id: updateCargoDto.batchId },
        });

        if (!batch) {
          throw new NotFoundException(
            `Batch with ID ${updateCargoDto.batchId} not found`,
          );
        }
      }

      // Check if status is changing
      const statusChanged =
        updateCargoDto.status && updateCargoDto.status !== cargo.status;
      const oldStatus = cargo.status;

      // Update cargo
      Object.assign(cargo, updateCargoDto);
      cargo.customer = customer;
      cargo.batch = batch;

      const updatedCargo = await this.cargoRepository.save(cargo);

      // Log the update
      await this.logsService.create(
        {
          event: 'Cargo Updated',
          message: `Cargo ${cargo.trackingNumber} updated${statusChanged ? ` - Status changed from ${oldStatus} to ${cargo.status}` : ''}`,
          associatedTable: 'cargos',
          associatedId: cargo.id,
        },
        account,
      );

      // If status changed, create notification and emit websocket event
      if (statusChanged) {
        await this.notificationService.create(
          {
            name: 'Cargo Status Updated',
            message: `Your cargo ${cargo.trackingNumber} status has been updated to ${cargo.status}`,
            associatedTable: 'cargos',
            associatedId: cargo.id,
          },
          account,
        );

        // Emit websocket event for status change
        this.websocketGateway.emitToUser(customer.id, 'cargo_status_changed', {
          id: updatedCargo.id,
          trackingNumber: updatedCargo.trackingNumber,
          oldStatus,
          newStatus: updatedCargo.status,
        });

        // Special handling for cargo release
        if (cargo.status === Status.RELEASED) {
          await this.handleCargoRelease(cargo, account);
        }
      }

      return updatedCargo;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update cargo');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const cargo = await this.findOne(id);

    try {
      // Don't actually delete, just mark as inactive
      cargo.status = Status.CANCELLED;
      await this.cargoRepository.save(cargo);

      // Log the deletion
      await this.logsService.create(
        {
          event: 'Cargo Cancelled',
          message: `Cargo ${cargo.trackingNumber} has been cancelled`,
          associatedTable: 'cargos',
          associatedId: cargo.id,
        },
        account,
      );

      // Notify the customer
      await this.notificationService.create(
        {
          name: 'Cargo Cancelled',
          message: `Your cargo ${cargo.trackingNumber} has been cancelled`,
          associatedTable: 'cargos',
          associatedId: cargo.id,
        },
        account,
      );

      // Emit websocket event
      this.websocketGateway.emitToUser(cargo.customer.id, 'cargo_cancelled', {
        id: cargo.id,
        trackingNumber: cargo.trackingNumber,
      });
    } catch (error) {
      throw new InternalServerErrorException('Failed to cancel cargo');
    }
  }

  private generateTrackingNumber(createCargoDto: CreateCargoDto): string {
    const prefix = createCargoDto.category === 'dangerous' ? 'DGR' : 'REG';
    const date = new Date();
    const year = date.getFullYear().toString().substr(-2);
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');

    return `${prefix}-${year}${month}-${random}`;
  }

  private async handleCargoRelease(
    cargo: Cargo,
    account: Account,
  ): Promise<void> {
    // Create notification for cargo release
    await this.notificationService.create(
      {
        name: 'Cargo Released',
        message: `Cargo ${cargo.trackingNumber} has been released for pickup`,
        associatedTable: 'cargos',
        associatedId: cargo.id,
      },
      account,
    );

    // Log the release event
    await this.logsService.create(
      {
        event: 'Cargo Released',
        message: `Cargo ${cargo.trackingNumber} has been released`,
        associatedTable: 'cargos',
        associatedId: cargo.id,
      },
      account,
    );

    // Emit websocket event
    this.websocketGateway.emitToUser(cargo.customer.id, 'cargo_released', {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
    });
  }
}
