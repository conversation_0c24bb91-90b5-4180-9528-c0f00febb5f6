import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { Customer } from '../entities/customer.entity';
import { Cargo } from '../entities/cargo.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateCustomerDto } from '../dto/create-customer.dto';
import { UpdateCustomerDto } from '../dto/update-customer.dto';
import { LogsService } from '../../system/services/logs.service';

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Cargo)
    private cargoRepository: Repository<Cargo>,
    private logsService: LogsService,
  ) {}

  async create(
    createCustomerDto: CreateCustomerDto,
    account: Account,
  ): Promise<Customer> {
    try {
      const customer = this.customerRepository.create({
        ...createCustomerDto,
        account,
      });

      const savedCustomer = await this.customerRepository.save(customer);

      // Log the customer creation
      await this.logsService.create(
        {
          event: 'Customer Created',
          message: `Customer ${createCustomerDto.name} created`,
          associatedTable: 'customers',
          associatedId: savedCustomer.id,
        },
        account,
      );

      return savedCustomer;
    } catch (error) {
      throw new InternalServerErrorException('Failed to create customer');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    status?: string;
    search?: string;
  }): Promise<{ customers: Customer[]; total: number }> {
    const { skip, take, status, search } = options;

    const queryBuilder = this.customerRepository
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .leftJoinAndSelect('customer.cargos', 'cargos')
      .where('customer.status != :inactiveStatus', {
        inactiveStatus: 'INACTIVE',
      });

    if (status) {
      queryBuilder.andWhere('customer.status = :status', { status });
    }

    if (search) {
      queryBuilder.andWhere(
        '(customer.name ILIKE :search OR customer.email ILIKE :search OR customer.phone ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    queryBuilder.skip(skip).take(take);

    const [customers, total] = await queryBuilder.getManyAndCount();

    return { customers, total };
  }

  async findOne(id: string): Promise<Customer> {
    const customer = await this.customerRepository.findOne({
      where: {
        id,
        status: Not('INACTIVE'),
      },
      relations: ['account', 'account.user', 'cargos'],
    });

    if (!customer) {
      throw new NotFoundException(`Customer with ID ${id} not found`);
    }

    return customer;
  }

  async update(
    id: string,
    updateCustomerDto: UpdateCustomerDto,
    account: Account,
  ): Promise<Customer> {
    const customer = await this.findOne(id);

    try {
      // Update customer
      Object.assign(customer, updateCustomerDto);

      const updatedCustomer = await this.customerRepository.save(customer);

      // Log the update
      await this.logsService.create(
        {
          event: 'Customer Updated',
          message: `Customer ${customer.name} updated`,
          associatedTable: 'customers',
          associatedId: customer.id,
        },
        account,
      );

      return updatedCustomer;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update customer');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const customer = await this.findOne(id);

    // Check if customer has cargos
    const cargoCount = await this.cargoRepository.count({
      where: { customer: { id: customer.id } },
    });

    if (cargoCount > 0) {
      throw new InternalServerErrorException(
        'Cannot delete customer with associated cargos',
      );
    }

    try {
      await this.customerRepository.remove(customer);

      // Log the deletion
      await this.logsService.create(
        {
          event: 'Customer Deleted',
          message: `Customer ${customer.name} deleted`,
          associatedTable: 'customers',
          associatedId: id,
        },
        account,
      );
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete customer');
    }
  }

  async findCustomerCargos(
    customerId: string,
    options: { skip: number; take: number },
  ): Promise<{ cargos: Cargo[]; total: number }> {
    const customer = await this.findOne(customerId);

    const queryBuilder = this.cargoRepository
      .createQueryBuilder('cargo')
      .leftJoinAndSelect('cargo.batch', 'batch')
      .leftJoinAndSelect('cargo.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .where('cargo.customer.id = :customerId', { customerId: customer.id })
      .andWhere('cargo.status != :inactiveStatus', {
        inactiveStatus: 'INACTIVE',
      })
      .skip(options.skip)
      .take(options.take);

    const [cargos, total] = await queryBuilder.getManyAndCount();

    return { cargos, total };
  }
}
